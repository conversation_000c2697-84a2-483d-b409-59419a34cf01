package com.yy.hd.web.deepresearch.service;

import com.yy.hd.web.deepresearch.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 研究图构建器
 * 负责根据研究请求构建有向图结构
 */
@Slf4j
@Service
public class ResearchGraphBuilder {
    
    /**
     * 构建研究图
     * @param request 研究请求
     * @return 研究图
     */
    public ResearchGraph buildGraph(ResearchRequest request) {
        log.info("Building research graph for topic: {}", request.getTopic());
        
        String graphId = generateGraphId();
        ResearchGraph graph = ResearchGraph.builder()
                .graphId(graphId)
                .topic(request.getTopic())
                .description(request.getDescription())
                .status(ResearchGraph.GraphStatus.CREATED)
                .createTime(LocalDateTime.now())
                .build();
        
        // 创建根节点
        ResearchNode rootNode = createRootNode(request);
        graph.addNode(rootNode);
        graph.setRootNodeId(rootNode.getNodeId());
        
        // 根据研究类型构建不同的图结构
        switch (request.getResearchType()) {
            case QUICK -> buildQuickResearchGraph(graph, request);
            case STANDARD -> buildStandardResearchGraph(graph, request);
            case COMPREHENSIVE -> buildComprehensiveResearchGraph(graph, request);
            case PROFESSIONAL -> buildProfessionalResearchGraph(graph, request);
        }
        
        log.info("Research graph built with {} nodes", graph.getNodes().size());
        return graph;
    }
    
    /**
     * 创建根节点
     * @param request 研究请求
     * @return 根节点
     */
    private ResearchNode createRootNode(ResearchRequest request) {
        return ResearchNode.builder()
                .nodeId(generateNodeId("root"))
                .nodeType(ResearchNode.NodeType.ROOT)
                .title("研究起点: " + request.getTopic())
                .description("开始深度研究: " + request.getTopic())
                .content(request.getDescription())
                .status(ResearchNode.NodeStatus.PENDING)
                .depth(0)
                .weight(1.0)
                .parentNodeIds(new ArrayList<>())
                .childNodeIds(new ArrayList<>())
                .metadata(new HashMap<>())
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 构建快速研究图
     * @param graph 研究图
     * @param request 研究请求
     */
    private void buildQuickResearchGraph(ResearchGraph graph, ResearchRequest request) {
        ResearchNode rootNode = graph.getRootNode();
        
        // 信息收集节点
        ResearchNode infoNode = createNode(
                ResearchNode.NodeType.INFORMATION_GATHERING,
                "信息收集",
                "收集关于 " + request.getTopic() + " 的基础信息",
                1, 0.8
        );
        graph.addNode(infoNode);
        graph.addEdge(rootNode.getNodeId(), infoNode.getNodeId());
        
        // 分析节点
        ResearchNode analysisNode = createNode(
                ResearchNode.NodeType.ANALYSIS,
                "基础分析",
                "对收集的信息进行基础分析",
                2, 0.7
        );
        graph.addNode(analysisNode);
        graph.addEdge(infoNode.getNodeId(), analysisNode.getNodeId());
        
        // 结论节点
        ResearchNode conclusionNode = createNode(
                ResearchNode.NodeType.CONCLUSION,
                "研究结论",
                "形成关于 " + request.getTopic() + " 的初步结论",
                3, 0.9
        );
        graph.addNode(conclusionNode);
        graph.addEdge(analysisNode.getNodeId(), conclusionNode.getNodeId());
    }
    
    /**
     * 构建标准研究图
     * @param graph 研究图
     * @param request 研究请求
     */
    private void buildStandardResearchGraph(ResearchGraph graph, ResearchRequest request) {
        ResearchNode rootNode = graph.getRootNode();
        
        // 问题分解节点
        ResearchNode decompositionNode = createNode(
                ResearchNode.NodeType.DECOMPOSITION,
                "问题分解",
                "将复杂问题分解为可研究的子问题",
                1, 0.9
        );
        graph.addNode(decompositionNode);
        graph.addEdge(rootNode.getNodeId(), decompositionNode.getNodeId());
        
        // 多个信息收集节点
        List<ResearchNode> infoNodes = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            ResearchNode infoNode = createNode(
                    ResearchNode.NodeType.INFORMATION_GATHERING,
                    "信息收集 " + i,
                    "收集第 " + i + " 部分相关信息",
                    2, 0.7
            );
            graph.addNode(infoNode);
            graph.addEdge(decompositionNode.getNodeId(), infoNode.getNodeId());
            infoNodes.add(infoNode);
        }
        
        // 分析节点
        ResearchNode analysisNode = createNode(
                ResearchNode.NodeType.ANALYSIS,
                "综合分析",
                "对所有收集的信息进行综合分析",
                3, 0.8
        );
        graph.addNode(analysisNode);
        for (ResearchNode infoNode : infoNodes) {
            graph.addEdge(infoNode.getNodeId(), analysisNode.getNodeId());
        }
        
        // 验证节点
        ResearchNode validationNode = createNode(
                ResearchNode.NodeType.VALIDATION,
                "结果验证",
                "验证分析结果的准确性和可靠性",
                4, 0.6
        );
        graph.addNode(validationNode);
        graph.addEdge(analysisNode.getNodeId(), validationNode.getNodeId());
        
        // 结论节点
        ResearchNode conclusionNode = createNode(
                ResearchNode.NodeType.CONCLUSION,
                "最终结论",
                "形成经过验证的研究结论",
                5, 1.0
        );
        graph.addNode(conclusionNode);
        graph.addEdge(validationNode.getNodeId(), conclusionNode.getNodeId());
    }
    
    /**
     * 构建综合研究图
     * @param graph 研究图
     * @param request 研究请求
     */
    private void buildComprehensiveResearchGraph(ResearchGraph graph, ResearchRequest request) {
        // 先构建标准图结构
        buildStandardResearchGraph(graph, request);
        
        // 添加额外的深度分析节点
        ResearchNode deepAnalysisNode = createNode(
                ResearchNode.NodeType.ANALYSIS,
                "深度分析",
                "进行更深入的专业分析",
                4, 0.8
        );
        graph.addNode(deepAnalysisNode);
        
        // 找到综合分析节点并连接
        ResearchNode analysisNode = findNodeByTitle(graph, "综合分析");
        if (analysisNode != null) {
            graph.addEdge(analysisNode.getNodeId(), deepAnalysisNode.getNodeId());
            
            // 重新连接验证节点
            ResearchNode validationNode = findNodeByTitle(graph, "结果验证");
            if (validationNode != null) {
                graph.addEdge(deepAnalysisNode.getNodeId(), validationNode.getNodeId());
            }
        }
        
        // 添加综合节点
        ResearchNode synthesisNode = createNode(
                ResearchNode.NodeType.SYNTHESIS,
                "综合整理",
                "综合所有研究成果",
                5, 0.9
        );
        graph.addNode(synthesisNode);
        
        ResearchNode validationNode = findNodeByTitle(graph, "结果验证");
        if (validationNode != null) {
            graph.addEdge(validationNode.getNodeId(), synthesisNode.getNodeId());
            
            // 重新连接结论节点
            ResearchNode conclusionNode = findNodeByTitle(graph, "最终结论");
            if (conclusionNode != null) {
                graph.addEdge(synthesisNode.getNodeId(), conclusionNode.getNodeId());
            }
        }
    }
    
    /**
     * 构建专业研究图
     * @param graph 研究图
     * @param request 研究请求
     */
    private void buildProfessionalResearchGraph(ResearchGraph graph, ResearchRequest request) {
        // 先构建综合图结构
        buildComprehensiveResearchGraph(graph, request);
        
        // 添加更多专业节点和交叉验证
        // 这里可以根据具体需求扩展
    }
    
    /**
     * 创建节点
     * @param nodeType 节点类型
     * @param title 标题
     * @param description 描述
     * @param depth 深度
     * @param weight 权重
     * @return 研究节点
     */
    private ResearchNode createNode(ResearchNode.NodeType nodeType, String title, 
                                   String description, int depth, double weight) {
        return ResearchNode.builder()
                .nodeId(generateNodeId(nodeType.getCode()))
                .nodeType(nodeType)
                .title(title)
                .description(description)
                .status(ResearchNode.NodeStatus.PENDING)
                .depth(depth)
                .weight(weight)
                .parentNodeIds(new ArrayList<>())
                .childNodeIds(new ArrayList<>())
                .metadata(new HashMap<>())
                .createTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 根据标题查找节点
     * @param graph 研究图
     * @param title 标题
     * @return 研究节点
     */
    private ResearchNode findNodeByTitle(ResearchGraph graph, String title) {
        return graph.getNodes().values().stream()
                .filter(node -> title.equals(node.getTitle()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 生成图ID
     * @return 图ID
     */
    private String generateGraphId() {
        return "graph_" + System.currentTimeMillis();
    }
    
    /**
     * 生成节点ID
     * @param prefix 前缀
     * @return 节点ID
     */
    private String generateNodeId(String prefix) {
        return prefix + "_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(new Random().nextInt());
    }
}
