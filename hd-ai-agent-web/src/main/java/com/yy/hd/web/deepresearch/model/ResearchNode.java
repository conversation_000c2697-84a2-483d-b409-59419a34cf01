package com.yy.hd.web.deepresearch.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 研究节点模型
 * 表示深度研究图中的一个节点
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchNode {
    
    /**
     * 节点唯一标识
     */
    private String nodeId;
    
    /**
     * 节点类型
     */
    private NodeType nodeType;
    
    /**
     * 节点标题
     */
    private String title;
    
    /**
     * 节点描述
     */
    private String description;
    
    /**
     * 节点内容
     */
    private String content;
    
    /**
     * 节点状态
     */
    private NodeStatus status;
    
    /**
     * 父节点ID列表
     */
    private List<String> parentNodeIds;
    
    /**
     * 子节点ID列表
     */
    private List<String> childNodeIds;
    
    /**
     * 节点深度（从根节点开始计算）
     */
    private Integer depth;
    
    /**
     * 节点权重（用于排序和优先级）
     */
    private Double weight;
    
    /**
     * 节点元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 节点类型枚举
     */
    public enum NodeType {
        /**
         * 根节点 - 研究的起始点
         */
        ROOT("root", "根节点"),
        
        /**
         * 问题分解节点 - 将复杂问题分解为子问题
         */
        DECOMPOSITION("decomposition", "问题分解"),
        
        /**
         * 信息收集节点 - 收集相关信息和数据
         */
        INFORMATION_GATHERING("information_gathering", "信息收集"),
        
        /**
         * 分析节点 - 对收集的信息进行分析
         */
        ANALYSIS("analysis", "分析处理"),
        
        /**
         * 综合节点 - 综合多个分析结果
         */
        SYNTHESIS("synthesis", "综合整理"),
        
        /**
         * 验证节点 - 验证结论的正确性
         */
        VALIDATION("validation", "验证确认"),
        
        /**
         * 结论节点 - 最终的研究结论
         */
        CONCLUSION("conclusion", "研究结论");
        
        private final String code;
        private final String description;
        
        NodeType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 节点状态枚举
     */
    public enum NodeStatus {
        /**
         * 待处理
         */
        PENDING("pending", "待处理"),
        
        /**
         * 处理中
         */
        PROCESSING("processing", "处理中"),
        
        /**
         * 已完成
         */
        COMPLETED("completed", "已完成"),
        
        /**
         * 失败
         */
        FAILED("failed", "处理失败"),
        
        /**
         * 已跳过
         */
        SKIPPED("skipped", "已跳过");
        
        private final String code;
        private final String description;
        
        NodeStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 检查节点是否可以执行
     * @return true if the node can be executed
     */
    public boolean canExecute() {
        return status == NodeStatus.PENDING || status == NodeStatus.FAILED;
    }
    
    /**
     * 检查节点是否已完成
     * @return true if the node is completed
     */
    public boolean isCompleted() {
        return status == NodeStatus.COMPLETED;
    }
    
    /**
     * 检查节点是否正在处理
     * @return true if the node is processing
     */
    public boolean isProcessing() {
        return status == NodeStatus.PROCESSING;
    }
    
    /**
     * 标记节点开始处理
     */
    public void markAsProcessing() {
        this.status = NodeStatus.PROCESSING;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 标记节点完成
     */
    public void markAsCompleted() {
        this.status = NodeStatus.COMPLETED;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 标记节点失败
     * @param errorMessage 错误信息
     */
    public void markAsFailed(String errorMessage) {
        this.status = NodeStatus.FAILED;
        this.errorMessage = errorMessage;
        this.updateTime = LocalDateTime.now();
    }
}
