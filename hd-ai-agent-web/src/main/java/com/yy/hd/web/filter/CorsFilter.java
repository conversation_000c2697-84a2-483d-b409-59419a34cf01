package com.yy.hd.web.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.regex.Pattern;

/**
 * 允许所有yy.com的域跨域请求本服务
 *
 * @author: <a href="mailto:<EMAIL>">李清洋</a>
 * @created: 2017年11月30日
 * @version: v1.00
 */
@Component
@Order(-1)
public class CorsFilter extends OncePerRequestFilter {

    private static final Pattern PATTERN = Pattern.compile(".*\\.yy\\.com:.*");

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        //跨域
        if (CorsUtils.isCorsRequest(request)) {

            String origin = request.getHeader(HttpHeaders.ORIGIN);

            boolean allow = isAllowCrossDomain(request);

            if (allow) {
                //通过
                response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, origin);
                response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_CREDENTIALS, Boolean.TRUE.toString());
                if (CorsUtils.isPreFlightRequest(request)) {
                    //OPTIONS
                    response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_METHODS, request.getHeader(HttpHeaders.ACCESS_CONTROL_REQUEST_METHOD));
                    response.addHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_HEADERS, request.getHeader(HttpHeaders.ACCESS_CONTROL_REQUEST_HEADERS));
                    return;
                }
                filterChain.doFilter(request, response);

            } else {
                //拒绝
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.getOutputStream().write("Invalid CORS request".getBytes(StandardCharsets.UTF_8));
                response.flushBuffer();
            }

        } else {
            filterChain.doFilter(request, response);
        }
    }

    private static boolean isAllowCrossDomain(HttpServletRequest request) {
        return true;
    }
}
