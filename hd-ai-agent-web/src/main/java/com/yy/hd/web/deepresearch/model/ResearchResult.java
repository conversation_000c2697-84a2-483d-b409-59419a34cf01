package com.yy.hd.web.deepresearch.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 深度研究结果模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchResult {
    
    /**
     * 研究ID
     */
    private String researchId;
    
    /**
     * 研究主题
     */
    private String topic;
    
    /**
     * 研究状态
     */
    private ResearchStatus status;
    
    /**
     * 研究图
     */
    private ResearchGraph graph;
    
    /**
     * 最终结论
     */
    private String conclusion;
    
    /**
     * 关键发现
     */
    private List<String> keyFindings;
    
    /**
     * 参考资料
     */
    private List<Reference> references;
    
    /**
     * 研究摘要
     */
    private String summary;
    
    /**
     * 置信度 (0.0 - 1.0)
     */
    private Double confidence;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 总耗时（毫秒）
     */
    private Long totalDuration;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 研究状态枚举
     */
    public enum ResearchStatus {
        PENDING("pending", "待开始"),
        RUNNING("running", "进行中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "失败"),
        CANCELLED("cancelled", "已取消"),
        TIMEOUT("timeout", "超时");
        
        private final String code;
        private final String description;
        
        ResearchStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 参考资料模型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Reference {
        /**
         * 标题
         */
        private String title;
        
        /**
         * URL
         */
        private String url;
        
        /**
         * 来源
         */
        private String source;
        
        /**
         * 摘要
         */
        private String summary;
        
        /**
         * 可信度
         */
        private Double credibility;
        
        /**
         * 获取时间
         */
        private LocalDateTime fetchTime;
    }
    
    /**
     * 检查研究是否完成
     * @return true if research is completed
     */
    public boolean isCompleted() {
        return status == ResearchStatus.COMPLETED;
    }
    
    /**
     * 检查研究是否失败
     * @return true if research failed
     */
    public boolean isFailed() {
        return status == ResearchStatus.FAILED || status == ResearchStatus.TIMEOUT;
    }
    
    /**
     * 检查研究是否正在进行
     * @return true if research is running
     */
    public boolean isRunning() {
        return status == ResearchStatus.RUNNING;
    }
    
    /**
     * 获取研究进度
     * @return 进度 (0.0 - 1.0)
     */
    public double getProgress() {
        if (graph == null) {
            return 0.0;
        }
        return graph.getProgress();
    }
}
