package com.yy.hd.web.service;


import com.yy.hd.commons.AgentChatReq;
import com.yy.hd.commons.AgentHttpHeaderKeys;
import com.yy.hd.commons.infoflow.InfoFlowMsgDecoder;
import com.yy.hd.commons.infoflow.InfoFlowReceiveMsg;
import com.yy.hd.commons.infoflow.InfoFlowRoboter;
import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.commons.utils.MD5Utils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Optional;

@Slf4j
@Service
public class ChatService {

    @Resource
    private RestTemplate loadBalancedRestTemplate;

    @Resource
    private InfoFlowService infoFlowService;

    @Resource
    private RetryTemplate retryTemplate;

    private static final String SOURCE_HEADER = AgentHttpHeaderKeys.SOURCE_HEADER;

    private static final String REFERER_SIGN_HEADER = AgentHttpHeaderKeys.REFERER_SIGN_HEADER;

    private static final String SOURCE_VALUE = "infoflow";

    private static final String REFERER_VALUE = "hd-ai-agent-web";

    public void chat(String source, String tool, InfoFlowReceiveMsg infoFlowReceiveMsg, InfoFlowRoboter roboter) {
        Assert.hasText(tool, "tool is empty");
        InfoFlowMsgDecoder.CommandAndText commandAndText = InfoFlowMsgDecoder.getCommandAndText(infoFlowReceiveMsg);
        String userId = infoFlowReceiveMsg.getMessage().getHeader().getFromuserid();
        String chatId = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        String question = commandAndText.text();
        String sourceValue = Optional.ofNullable(source).orElse(SOURCE_VALUE);
        AgentChatReq feedbackReq = new AgentChatReq(userId, chatId, question, false, tool);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        httpHeaders.add(SOURCE_HEADER, sourceValue);
        httpHeaders.add(HttpHeaders.REFERER, REFERER_VALUE);
        String sign = MD5Utils.getMD5(sourceValue + REFERER_VALUE) + RandomStringUtils.secure().nextAlphanumeric(6);
        httpHeaders.add(REFERER_SIGN_HEADER, sign);
        HttpEntity<AgentChatReq> httpEntity = new HttpEntity<>(feedbackReq, httpHeaders);
        int groupId = infoFlowReceiveMsg.getGroupid();
        String accessToken = roboter.getAccessToken();
        try {
            String feedbackRsp = retryTemplate.execute((RetryCallback<String, Throwable>) context -> {
                log.warn("chat fail, retry count:{}", context.getRetryCount(), context.getLastThrowable());
                return loadBalancedRestTemplate.postForObject(HttpUris.CHAT_URI, httpEntity, String.class);
            }, context -> {
                int retryCount = context.getRetryCount();
                return "服务器异常（已重试次数：" + retryCount + "），请联系管理员。";
            });
            infoFlowService.syncSend(groupId, accessToken, Collections.singletonList(userId), feedbackRsp);
        } catch (IllegalArgumentException e) {
            log.error("chat fail, msg:{}", e.getMessage());
            infoFlowService.syncSend(groupId, accessToken, Collections.singletonList(userId), e.getMessage());
        } catch (Throwable e) {
            log.error("chat error", e);
            infoFlowService.syncSend(groupId, accessToken, Collections.singletonList(userId), "服务器异常，请联系管理员。");
        }
    }


}
