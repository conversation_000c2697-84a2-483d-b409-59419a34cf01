package com.yy.hd.web.service;

import com.alibaba.fastjson.JSON;
import com.yy.bis.robot.BisRobotMessage;
import com.yy.bis.robot.BisRobotService;
import com.yy.hd.commons.infoflow.InfoFlowRoboter;
import com.yy.hd.commons.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> huangmin
 * @data : 2021/10/12
 */
@Slf4j
@Service
public class InfoFlowService {

    private final BisRobotService bisRobotService;

    private final Binder binder;

    public InfoFlowService(BisRobotService bisRobotService, Environment environment) {
        this.bisRobotService = bisRobotService;
        binder = Binder.get(environment);
    }

    public InfoFlowRoboter getRoboter(String roboterKey) {
        return binder.bindOrCreate("infoflow.roboters." + roboterKey, InfoFlowRoboter.class);
    }

    public void syncSend(Integer baiduGroupId, String webhook, Collection<String> userIds, String msg) {
        // markdown内容长度不能超过2048个字符
        List<String> subList = splitString(msg, 2048);
        for (int i = 0; i < subList.size(); i++) {
            String subMsg = subList.get(i);
            if (i == subList.size() - 1) {
                // 最后一段消息才@用户
                send(baiduGroupId, webhook, userIds, subMsg);
            } else {
                send(baiduGroupId, webhook, null, subMsg);
            }
        }
    }

    private void send(Integer baiduGroupId, String webhook, Collection<String> userIds, String msg) {
        BisRobotMessage message = BisRobotMessage.builder()
                .appendMarkdown(msg)
                .appendAt(userIds)
                .toids(Collections.singleton(baiduGroupId.longValue()));
        var ret = bisRobotService.syncSend(webhook, message);
        log.info("syncSend groupId:{}, userIds:{}, msg:{}, response:{}", baiduGroupId, JsonUtils.toJson(userIds), msg, JSON.toJSONString(ret));
    }

    private static List<String> splitString(String input, int length) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < input.length(); i += length) {
            int end = Math.min(i + length, input.length());
            result.add(input.substring(i, end));
        }
        if (result.size() > 1) {
            String lastMsg = result.getLast();
            result.set(result.size() - 1, lastMsg + "\n** tips：由于消息内容过长，已被分段发送，可能会影响正常的输出格式) **");
        }
        return result;
    }
}
