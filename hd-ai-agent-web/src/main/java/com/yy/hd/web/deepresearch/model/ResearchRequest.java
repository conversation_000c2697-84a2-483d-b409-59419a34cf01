package com.yy.hd.web.deepresearch.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.Map;

/**
 * 深度研究请求模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchRequest {
    
    /**
     * 研究主题
     */
    private String topic;
    
    /**
     * 研究描述
     */
    private String description;
    
    /**
     * 研究深度（最大层级）
     */
    @Builder.Default
    private Integer maxDepth = 5;
    
    /**
     * 最大节点数量
     */
    @Builder.Default
    private Integer maxNodes = 50;
    
    /**
     * 研究类型
     */
    @Builder.Default
    private ResearchType researchType = ResearchType.COMPREHENSIVE;
    
    /**
     * 是否启用并行处理
     */
    @Builder.Default
    private Boolean enableParallel = true;
    
    /**
     * 超时时间（秒）
     */
    @Builder.Default
    private Integer timeoutSeconds = 300;
    
    /**
     * 额外参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 研究类型枚举
     */
    public enum ResearchType {
        /**
         * 快速研究 - 基础信息收集
         */
        QUICK("quick", "快速研究"),
        
        /**
         * 标准研究 - 常规深度研究
         */
        STANDARD("standard", "标准研究"),
        
        /**
         * 综合研究 - 全面深入研究
         */
        COMPREHENSIVE("comprehensive", "综合研究"),
        
        /**
         * 专业研究 - 专业领域深度研究
         */
        PROFESSIONAL("professional", "专业研究");
        
        private final String code;
        private final String description;
        
        ResearchType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
