package com.yy.hd.web.controller;

import com.yy.hd.commons.infoflow.InfoFlowMsgDecoder;
import com.yy.hd.commons.infoflow.InfoFlowReceiveMsg;
import com.yy.hd.commons.infoflow.InfoFlowRoboter;
import com.yy.hd.web.service.ChatService;
import com.yy.hd.web.service.InfoFlowService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@AllArgsConstructor
@RequestMapping("/infoflow")
@RestController
public class InfoflowController {

    private final ChatService chatService;

    private final InfoFlowService infoFlowService;

    @PostMapping("/query")
    public void query(HttpServletRequest request, HttpServletResponse response, @RequestBody String params) throws Exception {
        String roboterKey = request.getParameter("roboter");
        String source = request.getParameter("source");
        String tool = request.getParameter("tool");
        log.info("query roboter:{}, source:{}, tool:{}, params:{}", roboterKey, source, tool, params);
        try {
            // 配置回调地址时应回调服务就绪，配置回调地址时会调用配置地址，需要回显才能校验通过
            String echostr = getEchostr(request, roboterKey, params);
            log.info("parse echostr:{}", echostr);
            if (StringUtils.isNotBlank(echostr)) {
                response.getWriter().print(echostr);
                return;
            }
            InfoFlowRoboter roboter = infoFlowService.getRoboter(roboterKey);
            InfoFlowReceiveMsg infoFlowReceiveMsg = InfoFlowMsgDecoder.decrypt(params, roboter.getEncodingAESKey());
            chatService.chat(source, tool, infoFlowReceiveMsg, roboter);
        } catch (Exception e) {
            log.error("query error", e);
        }
    }

    private String getEchostr(HttpServletRequest request, String roboterKey, String params) {
        String echostr = request.getParameter("echostr");
        if (StringUtils.isNotBlank(echostr)) {
            return echostr;
        } else if (params.contains("echostr")) {
            InfoFlowRoboter roboter = infoFlowService.getRoboter(roboterKey);
            return InfoFlowMsgDecoder.checkSignatureAndGetEchostr(params, roboter.getSignToken());
        }
        return null;
    }

}