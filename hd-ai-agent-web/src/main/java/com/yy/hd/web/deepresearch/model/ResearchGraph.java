package com.yy.hd.web.deepresearch.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 研究图模型
 * 表示整个深度研究的有向图结构
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchGraph {
    
    /**
     * 图的唯一标识
     */
    private String graphId;
    
    /**
     * 研究主题
     */
    private String topic;
    
    /**
     * 研究描述
     */
    private String description;
    
    /**
     * 图的状态
     */
    private GraphStatus status;
    
    /**
     * 节点映射 (nodeId -> ResearchNode)
     */
    @Builder.Default
    private Map<String, ResearchNode> nodes = new ConcurrentHashMap<>();
    
    /**
     * 边映射 (fromNodeId -> Set<toNodeId>)
     */
    @Builder.Default
    private Map<String, Set<String>> edges = new ConcurrentHashMap<>();
    
    /**
     * 反向边映射 (toNodeId -> Set<fromNodeId>)
     */
    @Builder.Default
    private Map<String, Set<String>> reverseEdges = new ConcurrentHashMap<>();
    
    /**
     * 根节点ID
     */
    private String rootNodeId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 完成时间
     */
    private LocalDateTime completeTime;
    
    /**
     * 图状态枚举
     */
    public enum GraphStatus {
        CREATED("created", "已创建"),
        PROCESSING("processing", "处理中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "处理失败"),
        CANCELLED("cancelled", "已取消");
        
        private final String code;
        private final String description;
        
        GraphStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 添加节点
     * @param node 研究节点
     */
    public void addNode(ResearchNode node) {
        nodes.put(node.getNodeId(), node);
        edges.putIfAbsent(node.getNodeId(), new HashSet<>());
        reverseEdges.putIfAbsent(node.getNodeId(), new HashSet<>());
        updateTime = LocalDateTime.now();
    }
    
    /**
     * 添加边
     * @param fromNodeId 起始节点ID
     * @param toNodeId 目标节点ID
     */
    public void addEdge(String fromNodeId, String toNodeId) {
        edges.computeIfAbsent(fromNodeId, k -> new HashSet<>()).add(toNodeId);
        reverseEdges.computeIfAbsent(toNodeId, k -> new HashSet<>()).add(fromNodeId);
        
        // 更新节点的父子关系
        ResearchNode fromNode = nodes.get(fromNodeId);
        ResearchNode toNode = nodes.get(toNodeId);
        
        if (fromNode != null) {
            if (fromNode.getChildNodeIds() == null) {
                fromNode.setChildNodeIds(new ArrayList<>());
            }
            if (!fromNode.getChildNodeIds().contains(toNodeId)) {
                fromNode.getChildNodeIds().add(toNodeId);
            }
        }
        
        if (toNode != null) {
            if (toNode.getParentNodeIds() == null) {
                toNode.setParentNodeIds(new ArrayList<>());
            }
            if (!toNode.getParentNodeIds().contains(fromNodeId)) {
                toNode.getParentNodeIds().add(fromNodeId);
            }
        }
        
        updateTime = LocalDateTime.now();
    }
    
    /**
     * 获取节点
     * @param nodeId 节点ID
     * @return 研究节点
     */
    public ResearchNode getNode(String nodeId) {
        return nodes.get(nodeId);
    }
    
    /**
     * 获取子节点
     * @param nodeId 节点ID
     * @return 子节点列表
     */
    public List<ResearchNode> getChildNodes(String nodeId) {
        Set<String> childIds = edges.get(nodeId);
        if (childIds == null || childIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return childIds.stream()
                .map(nodes::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取父节点
     * @param nodeId 节点ID
     * @return 父节点列表
     */
    public List<ResearchNode> getParentNodes(String nodeId) {
        Set<String> parentIds = reverseEdges.get(nodeId);
        if (parentIds == null || parentIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        return parentIds.stream()
                .map(nodes::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取可执行的节点（所有父节点都已完成）
     * @return 可执行的节点列表
     */
    public List<ResearchNode> getExecutableNodes() {
        return nodes.values().stream()
                .filter(node -> node.canExecute() && areParentsCompleted(node.getNodeId()))
                .sorted(Comparator.comparing(ResearchNode::getDepth, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(ResearchNode::getWeight, Comparator.nullsLast(Comparator.reverseOrder())))
                .collect(Collectors.toList());
    }
    
    /**
     * 检查节点的所有父节点是否都已完成
     * @param nodeId 节点ID
     * @return true if all parents are completed
     */
    private boolean areParentsCompleted(String nodeId) {
        Set<String> parentIds = reverseEdges.get(nodeId);
        if (parentIds == null || parentIds.isEmpty()) {
            return true; // 没有父节点，可以执行
        }
        
        return parentIds.stream()
                .map(nodes::get)
                .filter(Objects::nonNull)
                .allMatch(ResearchNode::isCompleted);
    }
    
    /**
     * 获取根节点
     * @return 根节点
     */
    public ResearchNode getRootNode() {
        return rootNodeId != null ? nodes.get(rootNodeId) : null;
    }
    
    /**
     * 检查图是否完成
     * @return true if all nodes are completed
     */
    public boolean isCompleted() {
        return nodes.values().stream()
                .allMatch(node -> node.isCompleted() || node.getStatus() == ResearchNode.NodeStatus.SKIPPED);
    }
    
    /**
     * 获取图的进度
     * @return 完成进度 (0.0 - 1.0)
     */
    public double getProgress() {
        if (nodes.isEmpty()) {
            return 0.0;
        }
        
        long completedCount = nodes.values().stream()
                .mapToLong(node -> node.isCompleted() || node.getStatus() == ResearchNode.NodeStatus.SKIPPED ? 1 : 0)
                .sum();
        
        return (double) completedCount / nodes.size();
    }
    
    /**
     * 获取图的统计信息
     * @return 统计信息
     */
    public GraphStatistics getStatistics() {
        Map<ResearchNode.NodeStatus, Long> statusCount = nodes.values().stream()
                .collect(Collectors.groupingBy(ResearchNode::getStatus, Collectors.counting()));
        
        return GraphStatistics.builder()
                .totalNodes(nodes.size())
                .completedNodes(statusCount.getOrDefault(ResearchNode.NodeStatus.COMPLETED, 0L).intValue())
                .processingNodes(statusCount.getOrDefault(ResearchNode.NodeStatus.PROCESSING, 0L).intValue())
                .pendingNodes(statusCount.getOrDefault(ResearchNode.NodeStatus.PENDING, 0L).intValue())
                .failedNodes(statusCount.getOrDefault(ResearchNode.NodeStatus.FAILED, 0L).intValue())
                .progress(getProgress())
                .build();
    }
    
    /**
     * 图统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GraphStatistics {
        private int totalNodes;
        private int completedNodes;
        private int processingNodes;
        private int pendingNodes;
        private int failedNodes;
        private double progress;
    }
}
