package com.yy.hd.web.config;

import com.yy.bis.http.HttpClient;
import com.yy.bis.http.optional.RestTemplateHttpClient;
import com.yy.bis.robot.BisRobotService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

@Configuration
public class InfoFlowConfig {

    @Bean
    public HttpClient bisHttpClient(RestTemplate restTemplate) {
        if (restTemplate != null) {
            return new RestTemplateHttpClient(restTemplate);
        }
        return new RestTemplateHttpClient(5000, 5000);
    }

    @Bean
    public BisRobotService bisRobotService() {
        return new BisRobotService("", null);
    }
}
