package com.yy.hd.web.deepresearch.service;

import com.yy.hd.web.deepresearch.model.ResearchGraph;
import com.yy.hd.web.deepresearch.model.ResearchNode;
import com.yy.hd.web.service.ChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 研究节点处理器
 * 负责处理单个研究节点的执行逻辑
 */
@Slf4j
@Service
public class ResearchNodeProcessor {
    
    @Autowired
    private ChatService chatService;
    
    /**
     * 处理研究节点
     * @param node 研究节点
     * @param graph 研究图
     */
    public void processNode(ResearchNode node, ResearchGraph graph) {
        log.info("Processing node: {} - {}", node.getNodeId(), node.getTitle());
        
        long startTime = System.currentTimeMillis();
        node.markAsProcessing();
        
        try {
            String result = executeNodeLogic(node, graph);
            node.setContent(result);
            node.markAsCompleted();
            
            long executionTime = System.currentTimeMillis() - startTime;
            node.setExecutionTime(executionTime);
            
            log.info("Node completed: {} in {}ms", node.getNodeId(), executionTime);
            
        } catch (Exception e) {
            log.error("Node processing failed: {}", node.getNodeId(), e);
            node.markAsFailed(e.getMessage());
        }
    }
    
    /**
     * 执行节点逻辑
     * @param node 研究节点
     * @param graph 研究图
     * @return 处理结果
     */
    private String executeNodeLogic(ResearchNode node, ResearchGraph graph) {
        return switch (node.getNodeType()) {
            case ROOT -> processRootNode(node, graph);
            case DECOMPOSITION -> processDecompositionNode(node, graph);
            case INFORMATION_GATHERING -> processInformationGatheringNode(node, graph);
            case ANALYSIS -> processAnalysisNode(node, graph);
            case SYNTHESIS -> processSynthesisNode(node, graph);
            case VALIDATION -> processValidationNode(node, graph);
            case CONCLUSION -> processConclusionNode(node, graph);
        };
    }
    
    /**
     * 处理根节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processRootNode(ResearchNode node, ResearchGraph graph) {
        String prompt = String.format(
                "开始深度研究主题: %s\n" +
                "研究描述: %s\n" +
                "请确认研究方向和目标。",
                graph.getTopic(),
                graph.getDescription()
        );
        
        return callChatService(prompt, "root_analysis");
    }
    
    /**
     * 处理问题分解节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processDecompositionNode(ResearchNode node, ResearchGraph graph) {
        String rootContent = getRootNodeContent(graph);
        
        String prompt = String.format(
                "基于以下研究主题，请将其分解为3-5个可独立研究的子问题：\n" +
                "主题: %s\n" +
                "背景: %s\n" +
                "请提供清晰的问题分解和每个子问题的研究重点。",
                graph.getTopic(),
                rootContent
        );
        
        return callChatService(prompt, "decomposition_analysis");
    }
    
    /**
     * 处理信息收集节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processInformationGatheringNode(ResearchNode node, ResearchGraph graph) {
        String parentContent = getParentNodesContent(node, graph);
        
        String prompt = String.format(
                "基于以下研究背景，请收集相关信息：\n" +
                "研究主题: %s\n" +
                "前置研究: %s\n" +
                "节点描述: %s\n" +
                "请提供详细的信息收集结果，包括关键数据、事实和参考资料。",
                graph.getTopic(),
                parentContent,
                node.getDescription()
        );
        
        return callChatService(prompt, "information_gathering");
    }
    
    /**
     * 处理分析节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processAnalysisNode(ResearchNode node, ResearchGraph graph) {
        String parentContent = getParentNodesContent(node, graph);
        
        String prompt = String.format(
                "基于以下收集的信息，请进行深入分析：\n" +
                "研究主题: %s\n" +
                "收集的信息: %s\n" +
                "分析要求: %s\n" +
                "请提供详细的分析结果，包括关键发现、趋势分析和深层洞察。",
                graph.getTopic(),
                parentContent,
                node.getDescription()
        );
        
        return callChatService(prompt, "analysis");
    }
    
    /**
     * 处理综合节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processSynthesisNode(ResearchNode node, ResearchGraph graph) {
        String parentContent = getParentNodesContent(node, graph);
        
        String prompt = String.format(
                "请综合以下所有研究成果：\n" +
                "研究主题: %s\n" +
                "研究成果: %s\n" +
                "请提供综合性的整理结果，突出关键发现和重要结论。",
                graph.getTopic(),
                parentContent
        );
        
        return callChatService(prompt, "synthesis");
    }
    
    /**
     * 处理验证节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processValidationNode(ResearchNode node, ResearchGraph graph) {
        String parentContent = getParentNodesContent(node, graph);
        
        String prompt = String.format(
                "请验证以下研究结果的准确性和可靠性：\n" +
                "研究主题: %s\n" +
                "待验证内容: %s\n" +
                "请提供验证结果，包括可信度评估、潜在问题和改进建议。",
                graph.getTopic(),
                parentContent
        );
        
        return callChatService(prompt, "validation");
    }
    
    /**
     * 处理结论节点
     * @param node 节点
     * @param graph 图
     * @return 处理结果
     */
    private String processConclusionNode(ResearchNode node, ResearchGraph graph) {
        String allContent = getAllPreviousContent(graph);
        
        String prompt = String.format(
                "基于整个研究过程，请形成最终结论：\n" +
                "研究主题: %s\n" +
                "完整研究过程: %s\n" +
                "请提供清晰、准确、有价值的最终结论，包括关键发现、实用建议和未来方向。",
                graph.getTopic(),
                allContent
        );
        
        return callChatService(prompt, "conclusion");
    }
    
    /**
     * 调用聊天服务
     * @param prompt 提示词
     * @param tool 工具名称
     * @return 响应结果
     */
    private String callChatService(String prompt, String tool) {
        try {
            // 这里调用现有的ChatService
            // 注意：需要适配现有的ChatService接口
            return chatService.processMessage(prompt, tool, false);
        } catch (Exception e) {
            log.error("Error calling chat service", e);
            return "处理失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取根节点内容
     * @param graph 研究图
     * @return 根节点内容
     */
    private String getRootNodeContent(ResearchGraph graph) {
        ResearchNode rootNode = graph.getRootNode();
        return rootNode != null && rootNode.getContent() != null ? 
               rootNode.getContent() : graph.getDescription();
    }
    
    /**
     * 获取父节点内容
     * @param node 当前节点
     * @param graph 研究图
     * @return 父节点内容
     */
    private String getParentNodesContent(ResearchNode node, ResearchGraph graph) {
        if (node.getParentNodeIds() == null || node.getParentNodeIds().isEmpty()) {
            return getRootNodeContent(graph);
        }
        
        StringBuilder content = new StringBuilder();
        for (String parentId : node.getParentNodeIds()) {
            ResearchNode parentNode = graph.getNode(parentId);
            if (parentNode != null && parentNode.getContent() != null) {
                content.append("【").append(parentNode.getTitle()).append("】\n");
                content.append(parentNode.getContent()).append("\n\n");
            }
        }
        
        return content.toString();
    }
    
    /**
     * 获取所有前置内容
     * @param graph 研究图
     * @return 所有前置内容
     */
    private String getAllPreviousContent(ResearchGraph graph) {
        StringBuilder content = new StringBuilder();
        
        graph.getNodes().values().stream()
                .filter(node -> node.isCompleted() && node.getContent() != null)
                .sorted((n1, n2) -> {
                    if (n1.getDepth() != null && n2.getDepth() != null) {
                        return n1.getDepth().compareTo(n2.getDepth());
                    }
                    return n1.getCreateTime().compareTo(n2.getCreateTime());
                })
                .forEach(node -> {
                    content.append("【").append(node.getTitle()).append("】\n");
                    content.append(node.getContent()).append("\n\n");
                });
        
        return content.toString();
    }
}
