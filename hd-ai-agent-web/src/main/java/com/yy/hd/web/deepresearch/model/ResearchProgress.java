package com.yy.hd.web.deepresearch.model;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 研究进度模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResearchProgress {
    
    /**
     * 研究ID
     */
    private String researchId;
    
    /**
     * 研究状态
     */
    private ResearchResult.ResearchStatus status;
    
    /**
     * 进度 (0.0 - 1.0)
     */
    private double progress;
    
    /**
     * 当前处理的节点
     */
    private String currentNode;
    
    /**
     * 图统计信息
     */
    private ResearchGraph.GraphStatistics statistics;
}
