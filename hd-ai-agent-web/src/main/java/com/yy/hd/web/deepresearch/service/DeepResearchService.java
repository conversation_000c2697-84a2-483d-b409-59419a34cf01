package com.yy.hd.web.deepresearch.service;

import com.yy.hd.web.deepresearch.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 深度研究服务
 * 负责管理和执行深度研究任务
 */
@Slf4j
@Service
public class DeepResearchService {
    
    private final Map<String, ResearchResult> researchCache = new ConcurrentHashMap<>();
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);
    
    @Autowired
    private ResearchGraphBuilder graphBuilder;
    
    @Autowired
    private ResearchNodeProcessor nodeProcessor;
    
    @Autowired
    private ResearchAnalyzer analyzer;
    
    /**
     * 开始深度研究
     * @param request 研究请求
     * @return 研究结果
     */
    public CompletableFuture<ResearchResult> startResearch(ResearchRequest request) {
        String researchId = generateResearchId();
        log.info("Starting deep research: {} for topic: {}", researchId, request.getTopic());
        
        // 创建初始研究结果
        ResearchResult result = ResearchResult.builder()
                .researchId(researchId)
                .topic(request.getTopic())
                .status(ResearchResult.ResearchStatus.PENDING)
                .startTime(LocalDateTime.now())
                .keyFindings(new ArrayList<>())
                .references(new ArrayList<>())
                .metadata(new HashMap<>())
                .build();
        
        researchCache.put(researchId, result);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return executeResearch(request, result);
            } catch (Exception e) {
                log.error("Research failed: {}", researchId, e);
                result.setStatus(ResearchResult.ResearchStatus.FAILED);
                result.setErrorMessage(e.getMessage());
                result.setEndTime(LocalDateTime.now());
                return result;
            }
        }, executorService);
    }
    
    /**
     * 执行研究
     * @param request 研究请求
     * @param result 研究结果
     * @return 完成的研究结果
     */
    private ResearchResult executeResearch(ResearchRequest request, ResearchResult result) {
        result.setStatus(ResearchResult.ResearchStatus.RUNNING);
        
        try {
            // 1. 构建研究图
            log.info("Building research graph for: {}", request.getTopic());
            ResearchGraph graph = graphBuilder.buildGraph(request);
            result.setGraph(graph);
            
            // 2. 执行研究节点
            log.info("Executing research nodes for: {}", request.getTopic());
            executeResearchNodes(graph, request);
            
            // 3. 分析研究结果
            log.info("Analyzing research results for: {}", request.getTopic());
            analyzer.analyzeResults(result);
            
            // 4. 标记完成
            result.setStatus(ResearchResult.ResearchStatus.COMPLETED);
            result.setEndTime(LocalDateTime.now());
            result.setTotalDuration(calculateDuration(result.getStartTime(), result.getEndTime()));
            
            log.info("Research completed: {} for topic: {}", result.getResearchId(), request.getTopic());
            
        } catch (Exception e) {
            log.error("Error executing research: {}", result.getResearchId(), e);
            result.setStatus(ResearchResult.ResearchStatus.FAILED);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(LocalDateTime.now());
        }
        
        return result;
    }
    
    /**
     * 执行研究节点
     * @param graph 研究图
     * @param request 研究请求
     */
    private void executeResearchNodes(ResearchGraph graph, ResearchRequest request) {
        graph.setStatus(ResearchGraph.GraphStatus.PROCESSING);
        
        while (!graph.isCompleted()) {
            List<ResearchNode> executableNodes = graph.getExecutableNodes();
            
            if (executableNodes.isEmpty()) {
                log.warn("No executable nodes found, breaking execution loop");
                break;
            }
            
            if (request.getEnableParallel()) {
                // 并行执行
                List<CompletableFuture<Void>> futures = executableNodes.stream()
                        .map(node -> CompletableFuture.runAsync(() -> 
                            nodeProcessor.processNode(node, graph), executorService))
                        .toList();
                
                // 等待所有节点完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } else {
                // 串行执行
                for (ResearchNode node : executableNodes) {
                    nodeProcessor.processNode(node, graph);
                }
            }
        }
        
        graph.setStatus(ResearchGraph.GraphStatus.COMPLETED);
        graph.setCompleteTime(LocalDateTime.now());
    }
    
    /**
     * 获取研究结果
     * @param researchId 研究ID
     * @return 研究结果
     */
    public Optional<ResearchResult> getResearchResult(String researchId) {
        return Optional.ofNullable(researchCache.get(researchId));
    }
    
    /**
     * 获取研究进度
     * @param researchId 研究ID
     * @return 进度信息
     */
    public Optional<ResearchProgress> getResearchProgress(String researchId) {
        return getResearchResult(researchId)
                .map(result -> ResearchProgress.builder()
                        .researchId(researchId)
                        .status(result.getStatus())
                        .progress(result.getProgress())
                        .currentNode(getCurrentProcessingNode(result.getGraph()))
                        .statistics(result.getGraph() != null ? result.getGraph().getStatistics() : null)
                        .build());
    }
    
    /**
     * 取消研究
     * @param researchId 研究ID
     * @return 是否成功取消
     */
    public boolean cancelResearch(String researchId) {
        ResearchResult result = researchCache.get(researchId);
        if (result != null && result.isRunning()) {
            result.setStatus(ResearchResult.ResearchStatus.CANCELLED);
            result.setEndTime(LocalDateTime.now());
            log.info("Research cancelled: {}", researchId);
            return true;
        }
        return false;
    }
    
    /**
     * 清理完成的研究
     * @param olderThanHours 清理多少小时前的研究
     */
    public void cleanupCompletedResearch(int olderThanHours) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(olderThanHours);
        
        researchCache.entrySet().removeIf(entry -> {
            ResearchResult result = entry.getValue();
            return (result.isCompleted() || result.isFailed()) 
                    && result.getEndTime() != null 
                    && result.getEndTime().isBefore(cutoffTime);
        });
        
        log.info("Cleaned up research cache, remaining entries: {}", researchCache.size());
    }
    
    /**
     * 生成研究ID
     * @return 研究ID
     */
    private String generateResearchId() {
        return "research_" + System.currentTimeMillis() + "_" + 
               Integer.toHexString(new Random().nextInt());
    }
    
    /**
     * 计算持续时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 持续时间（毫秒）
     */
    private Long calculateDuration(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null) {
            return null;
        }
        return java.time.Duration.between(startTime, endTime).toMillis();
    }
    
    /**
     * 获取当前正在处理的节点
     * @param graph 研究图
     * @return 当前节点信息
     */
    private String getCurrentProcessingNode(ResearchGraph graph) {
        if (graph == null) {
            return null;
        }
        
        return graph.getNodes().values().stream()
                .filter(ResearchNode::isProcessing)
                .findFirst()
                .map(node -> node.getTitle() != null ? node.getTitle() : node.getNodeId())
                .orElse(null);
    }
    

}
