package com.yy.hd.web.deepresearch.service;

import com.yy.hd.web.deepresearch.model.ResearchGraph;
import com.yy.hd.web.deepresearch.model.ResearchNode;
import com.yy.hd.web.deepresearch.model.ResearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 研究分析器
 * 负责分析研究结果并生成最终报告
 */
@Slf4j
@Service
public class ResearchAnalyzer {
    
    /**
     * 分析研究结果
     * @param result 研究结果
     */
    public void analyzeResults(ResearchResult result) {
        log.info("Analyzing research results for: {}", result.getResearchId());
        
        ResearchGraph graph = result.getGraph();
        if (graph == null) {
            log.warn("No research graph found for analysis");
            return;
        }
        
        // 提取关键发现
        extractKeyFindings(result, graph);
        
        // 生成研究摘要
        generateSummary(result, graph);
        
        // 生成最终结论
        generateConclusion(result, graph);
        
        // 提取参考资料
        extractReferences(result, graph);
        
        // 计算置信度
        calculateConfidence(result, graph);
        
        log.info("Research analysis completed for: {}", result.getResearchId());
    }
    
    /**
     * 提取关键发现
     * @param result 研究结果
     * @param graph 研究图
     */
    private void extractKeyFindings(ResearchResult result, ResearchGraph graph) {
        List<String> keyFindings = new ArrayList<>();
        
        // 从分析节点提取关键发现
        graph.getNodes().values().stream()
                .filter(node -> node.getNodeType() == ResearchNode.NodeType.ANALYSIS && node.isCompleted())
                .forEach(node -> {
                    String content = node.getContent();
                    if (content != null && !content.trim().isEmpty()) {
                        // 简单的关键信息提取（实际项目中可以使用更复杂的NLP技术）
                        List<String> findings = extractImportantSentences(content);
                        keyFindings.addAll(findings);
                    }
                });
        
        // 从综合节点提取关键发现
        graph.getNodes().values().stream()
                .filter(node -> node.getNodeType() == ResearchNode.NodeType.SYNTHESIS && node.isCompleted())
                .forEach(node -> {
                    String content = node.getContent();
                    if (content != null && !content.trim().isEmpty()) {
                        List<String> findings = extractImportantSentences(content);
                        keyFindings.addAll(findings);
                    }
                });
        
        // 去重并限制数量
        List<String> uniqueFindings = keyFindings.stream()
                .distinct()
                .limit(10)
                .collect(Collectors.toList());
        
        result.setKeyFindings(uniqueFindings);
    }
    
    /**
     * 生成研究摘要
     * @param result 研究结果
     * @param graph 研究图
     */
    private void generateSummary(ResearchResult result, ResearchGraph graph) {
        StringBuilder summary = new StringBuilder();
        
        summary.append("本次深度研究围绕主题「").append(result.getTopic()).append("」展开，");
        summary.append("通过系统性的信息收集、分析和验证，得出以下研究成果：\n\n");
        
        // 添加研究过程概述
        long completedNodes = graph.getNodes().values().stream()
                .mapToLong(node -> node.isCompleted() ? 1 : 0)
                .sum();
        
        summary.append("研究过程包含 ").append(completedNodes).append(" 个研究节点，");
        summary.append("涵盖了问题分解、信息收集、深度分析、结果验证等多个环节。\n\n");
        
        // 添加关键发现概述
        if (result.getKeyFindings() != null && !result.getKeyFindings().isEmpty()) {
            summary.append("主要发现包括：\n");
            for (int i = 0; i < Math.min(3, result.getKeyFindings().size()); i++) {
                summary.append("• ").append(result.getKeyFindings().get(i)).append("\n");
            }
        }
        
        result.setSummary(summary.toString());
    }
    
    /**
     * 生成最终结论
     * @param result 研究结果
     * @param graph 研究图
     */
    private void generateConclusion(ResearchResult result, ResearchGraph graph) {
        // 查找结论节点
        ResearchNode conclusionNode = graph.getNodes().values().stream()
                .filter(node -> node.getNodeType() == ResearchNode.NodeType.CONCLUSION && node.isCompleted())
                .findFirst()
                .orElse(null);
        
        if (conclusionNode != null && conclusionNode.getContent() != null) {
            result.setConclusion(conclusionNode.getContent());
        } else {
            // 如果没有专门的结论节点，从其他节点综合生成结论
            StringBuilder conclusion = new StringBuilder();
            conclusion.append("基于本次深度研究，关于「").append(result.getTopic()).append("」的主要结论如下：\n\n");
            
            // 从验证节点获取结论
            graph.getNodes().values().stream()
                    .filter(node -> node.getNodeType() == ResearchNode.NodeType.VALIDATION && node.isCompleted())
                    .findFirst()
                    .ifPresent(node -> conclusion.append(node.getContent()));
            
            result.setConclusion(conclusion.toString());
        }
    }
    
    /**
     * 提取参考资料
     * @param result 研究结果
     * @param graph 研究图
     */
    private void extractReferences(ResearchResult result, ResearchGraph graph) {
        List<ResearchResult.Reference> references = new ArrayList<>();
        
        // 从信息收集节点提取参考资料
        graph.getNodes().values().stream()
                .filter(node -> node.getNodeType() == ResearchNode.NodeType.INFORMATION_GATHERING && node.isCompleted())
                .forEach(node -> {
                    String content = node.getContent();
                    if (content != null) {
                        // 简单的URL提取（实际项目中可以使用更复杂的正则表达式）
                        List<String> urls = extractUrls(content);
                        for (String url : urls) {
                            ResearchResult.Reference reference = ResearchResult.Reference.builder()
                                    .title("参考资料")
                                    .url(url)
                                    .source(node.getTitle())
                                    .summary("从研究节点提取的参考资料")
                                    .credibility(0.8)
                                    .fetchTime(node.getUpdateTime())
                                    .build();
                            references.add(reference);
                        }
                    }
                });
        
        result.setReferences(references);
    }
    
    /**
     * 计算置信度
     * @param result 研究结果
     * @param graph 研究图
     */
    private void calculateConfidence(ResearchResult result, ResearchGraph graph) {
        double confidence = 0.5; // 基础置信度
        
        // 根据完成的节点数量调整置信度
        double completionRate = graph.getProgress();
        confidence += completionRate * 0.3;
        
        // 根据验证节点的存在调整置信度
        boolean hasValidation = graph.getNodes().values().stream()
                .anyMatch(node -> node.getNodeType() == ResearchNode.NodeType.VALIDATION && node.isCompleted());
        if (hasValidation) {
            confidence += 0.1;
        }
        
        // 根据综合节点的存在调整置信度
        boolean hasSynthesis = graph.getNodes().values().stream()
                .anyMatch(node -> node.getNodeType() == ResearchNode.NodeType.SYNTHESIS && node.isCompleted());
        if (hasSynthesis) {
            confidence += 0.1;
        }
        
        // 确保置信度在合理范围内
        confidence = Math.max(0.0, Math.min(1.0, confidence));
        
        result.setConfidence(confidence);
    }
    
    /**
     * 提取重要句子
     * @param content 内容
     * @return 重要句子列表
     */
    private List<String> extractImportantSentences(String content) {
        List<String> sentences = new ArrayList<>();
        
        // 简单的句子提取逻辑
        String[] lines = content.split("\n");
        for (String line : lines) {
            line = line.trim();
            if (line.length() > 20 && line.length() < 200) {
                // 查找包含关键词的句子
                if (containsKeywords(line)) {
                    sentences.add(line);
                }
            }
        }
        
        return sentences.stream().limit(5).collect(Collectors.toList());
    }
    
    /**
     * 检查是否包含关键词
     * @param sentence 句子
     * @return 是否包含关键词
     */
    private boolean containsKeywords(String sentence) {
        String[] keywords = {"发现", "结论", "重要", "关键", "显示", "表明", "证明", "分析", "研究"};
        String lowerSentence = sentence.toLowerCase();
        
        for (String keyword : keywords) {
            if (lowerSentence.contains(keyword)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 提取URL
     * @param content 内容
     * @return URL列表
     */
    private List<String> extractUrls(String content) {
        List<String> urls = new ArrayList<>();
        
        // 简单的URL提取（实际项目中应该使用正则表达式）
        String[] words = content.split("\\s+");
        for (String word : words) {
            if (word.startsWith("http://") || word.startsWith("https://")) {
                urls.add(word);
            }
        }
        
        return urls.stream().limit(10).collect(Collectors.toList());
    }
}
