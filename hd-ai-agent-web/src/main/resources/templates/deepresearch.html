<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度研究平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/dist/vis-network.min.css" rel="stylesheet">
    <style>
        .research-card {
            transition: all 0.3s ease;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .research-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }
        #graphContainer {
            height: 500px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .node-details {
            max-height: 300px;
            overflow-y: auto;
        }
        .progress-container {
            position: relative;
            padding: 20px;
        }
        .progress-label {
            position: absolute;
            right: 30px;
            top: 20px;
        }
        .key-finding {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 6px;
            margin-bottom: 10px;
            border-left: 4px solid #0d6efd;
        }
        .reference-item {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary">
    <div class="container">
        <a class="navbar-brand" href="#">深度研究平台</a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link active" href="#newResearch">新建研究</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#researchList">研究列表</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="container mt-4">
    <!-- 新建研究表单 -->
    <div id="newResearch" class="card mb-4 research-card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">新建深度研究</h5>
        </div>
        <div class="card-body">
            <form id="researchForm">
                <div class="mb-3">
                    <label for="topic" class="form-label">研究主题</label>
                    <input type="text" class="form-control" id="topic" required placeholder="输入研究主题，例如：人工智能在医疗领域的应用">
                </div>
                <div class="mb-3">
                    <label for="description" class="form-label">研究描述</label>
                    <textarea class="form-control" id="description" rows="3" placeholder="详细描述研究目标和范围"></textarea>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label for="researchType" class="form-label">研究类型</label>
                        <select class="form-select" id="researchType">
                            <option value="QUICK">快速研究 - 基础信息收集</option>
                            <option value="STANDARD">标准研究 - 常规深度研究</option>
                            <option value="COMPREHENSIVE" selected>综合研究 - 全面深入研究</option>
                            <option value="PROFESSIONAL">专业研究 - 专业领域深度研究</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="maxDepth" class="form-label">研究深度</label>
                        <input type="range" class="form-range" id="maxDepth" min="3" max="10" value="5">
                        <div class="d-flex justify-content-between">
                            <small>浅</small>
                            <small id="depthValue">5</small>
                            <small>深</small>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="enableParallel" checked>
                            <label class="form-check-label" for="enableParallel">启用并行处理</label>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="timeoutSeconds" class="form-label">超时时间（秒）</label>
                        <input type="number" class="form-control" id="timeoutSeconds" value="300" min="60">
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">开始研究</button>
            </form>
        </div>
    </div>

    <!-- 研究进度 -->
    <div id="researchProgress" class="card mb-4 research-card d-none">
        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">研究进度</h5>
            <button id="cancelResearch" class="btn btn-sm btn-light">取消研究</button>
        </div>
        <div class="card-body">
            <h5 id="progressTopic" class="card-title"></h5>
            <div class="progress-container">
                <div class="progress" style="height: 25px;">
                    <div id="progressBar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                </div>
                <span class="progress-label"><span id="progressPercent">0</span>%</span>
            </div>
            <div class="mt-3">
                <p><strong>状态：</strong> <span id="progressStatus">准备中</span></p>
                <p><strong>当前节点：</strong> <span id="currentNode">-</span></p>
                <div id="nodeStats" class="mt-3">
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <h6>总节点</h6>
                                <span id="totalNodes">0</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <h6>已完成</h6>
                                <span id="completedNodes">0</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <h6>进行中</h6>
                                <span id="runningNodes">0</span>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="border rounded p-2">
                                <h6>等待中</h6>
                                <span id="pendingNodes">0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 研究结果 -->
    <div id="researchResult" class="card mb-4 research-card d-none">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">研究结果</h5>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs" id="resultTabs">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#summary">摘要</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#findings">关键发现</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#conclusion">结论</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#references">参考资料</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#graph">研究图</a>
                </li>
            </ul>
            <div class="tab-content mt-3">
                <div class="tab-pane fade show active" id="summary">
                    <h5>研究摘要</h5>
                    <div id="summaryContent" class="mt-3"></div>
                    <div class="mt-3">
                        <p><strong>研究ID：</strong> <span id="researchId"></span></p>
                        <p><strong>开始时间：</strong> <span id="startTime"></span></p>
                        <p><strong>结束时间：</strong> <span id="endTime"></span></p>
                        <p><strong>总耗时：</strong> <span id="totalDuration"></span></p>
                        <p><strong>置信度：</strong> <span id="confidence"></span></p>
                    </div>
                </div>
                <div class="tab-pane fade" id="findings">
                    <h5>关键发现</h5>
                    <div id="findingsContent" class="mt-3"></div>
                </div>
                <div class="tab-pane fade" id="conclusion">
                    <h5>研究结论</h5>
                    <div id="conclusionContent" class="mt-3"></div>
                </div>
                <div class="tab-pane fade" id="references">
                    <h5>参考资料</h5>
                    <div id="referencesContent" class="mt-3"></div>
                </div>
                <div class="tab-pane fade" id="graph">
                    <h5>研究图</h5>
                    <div id="graphContainer" class="mt-3"></div>
                    <div class="mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">节点详情</h6>
                            </div>
                            <div class="card-body node-details" id="nodeDetails">
                                <p class="text-muted">点击图中节点查看详情</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 研究列表 -->
    <div id="researchList" class="card research-card">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">历史研究</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                    <tr>
                        <th>ID</th>
                        <th>主题</th>
                        <th>状态</th>
                        <th>开始时间</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="researchListBody">
                    <tr>
                        <td colspan="5" class="text-center">暂无研究记录</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/vis-network@9.1.2/dist/vis-network.min.js"></script>
<script>
    // 全局变量
    let currentResearchId = null;
    let progressInterval = null;
    let researchGraph = null;
    let network = null;

    // DOM 加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        // 深度滑块值显示
        document.getElementById('maxDepth').addEventListener('input', function() {
            document.getElementById('depthValue').textContent = this.value;
        });

        // 表单提交
        document.getElementById('researchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startResearch();
        });

        // 取消研究按钮
        document.getElementById('cancelResearch').addEventListener('click', function() {
            cancelResearch();
        });

        // 加载历史研究列表
        loadResearchList();
    });

    // 开始研究
    function startResearch() {
        const topic = document.getElementById('topic').value;
        const description = document.getElementById('description').value;
        const researchType = document.getElementById('researchType').value;
        const maxDepth = document.getElementById('maxDepth').value;
        const enableParallel = document.getElementById('enableParallel').checked;
        const timeoutSeconds = document.getElementById('timeoutSeconds').value;

        const requestData = {
            topic: topic,
            description: description,
            researchType: researchType,
            maxDepth: parseInt(maxDepth),
            enableParallel: enableParallel,
            timeoutSeconds: parseInt(timeoutSeconds)
        };

        // 显示进度区域
        document.getElementById('researchProgress').classList.remove('d-none');
        document.getElementById('progressTopic').textContent = topic;

        // 隐藏结果区域
        document.getElementById('researchResult').classList.add('d-none');

        // 发送请求
        fetch('/api/deepresearch/start', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
            .then(response => response.json())
            .then(data => {
                currentResearchId = data.researchId;

                // 开始轮询进度
                startProgressPolling(currentResearchId);

                // 添加到研究列表
                addToResearchList({
                    researchId: currentResearchId,
                    topic: topic,
                    status: data.status,
                    startTime: new Date().toLocaleString()
                });
            })
            .catch(error => {
                console.error('Error starting research:', error);
                alert('启动研究失败: ' + error);
            });
    }

    // 开始轮询进度
    function startProgressPolling(researchId) {
        // 清除之前的轮询
        if (progressInterval) {
            clearInterval(progressInterval);
        }

        // 设置轮询
        progressInterval = setInterval(() => {
            fetch(`/api/deepresearch/progress/${researchId}`)
                .then(response => response.json())
                .then(data => {
                    updateProgressUI(data);

                    // 如果研究完成或失败，停止轮询并加载结果
                    if (data.status === 'COMPLETED' || data.status === 'FAILED' ||
                        data.status === 'CANCELLED' || data.status === 'TIMEOUT') {
                        clearInterval(progressInterval);
                        loadResearchResult(researchId);
                    }
                })
                .catch(error => {
                    console.error('Error fetching progress:', error);
                    clearInterval(progressInterval);
                });
        }, 2000); // 每2秒轮询一次
    }

    // 更新进度UI
    function updateProgressUI(data) {
        const progressPercent = Math.round(data.progress * 100);
        document.getElementById('progressBar').style.width = `${progressPercent}%`;
        document.getElementById('progressPercent').textContent = progressPercent;

        // 更新状态
        let statusText = '未知';
        switch (data.status) {
            case 'PENDING': statusText = '准备中'; break;
            case 'RUNNING': statusText = '进行中'; break;
            case 'COMPLETED': statusText = '已完成'; break;
            case 'FAILED': statusText = '失败'; break;
            case 'CANCELLED': statusText = '已取消'; break;
            case 'TIMEOUT': statusText = '超时'; break;
        }
        document.getElementById('progressStatus').textContent = statusText;

        // 更新当前节点
        document.getElementById('currentNode').textContent = data.currentNode || '-';

        // 更新节点统计
        if (data.statistics) {
            document.getElementById('totalNodes').textContent = data.statistics.totalNodes;
            document.getElementById('completedNodes').textContent = data.statistics.completedNodes;
            document.getElementById('runningNodes').textContent = data.statistics.runningNodes;
            document.getElementById('pendingNodes').textContent = data.statistics.pendingNodes;
        }

        // 更新研究列表中的状态
        updateResearchListStatus(data.researchId, data.status);
    }

    // 加载研究结果
    function loadResearchResult(researchId) {
        fetch(`/api/deepresearch/result/${researchId}`)
            .then(response => response.json())
            .then(data => {
                // 显示结果区域
                document.getElementById('researchResult').classList.remove('d-none');

                // 填充结果数据
                document.getElementById('researchId').textContent = data.researchId;
                document.getElementById('summaryContent').innerHTML = formatText(data.summary);
                document.getElementById('conclusionContent').innerHTML = formatText(data.conclusion);

                // 格式化时间
                const startTime = new Date(data.startTime).toLocaleString();
                const endTime = new Date(data.endTime).toLocaleString();
                document.getElementById('startTime').textContent = startTime;
                document.getElementById('endTime').textContent = endTime;

                // 计算耗时
                const duration = data.totalDuration ? formatDuration(data.totalDuration) : '-';
                document.getElementById('totalDuration').textContent = duration;

                // 置信度
                const confidence = data.confidence ? (data.confidence * 100).toFixed(1) + '%' : '-';
                document.getElementById('confidence').textContent = confidence;

                // 关键发现
                const findingsContent = document.getElementById('findingsContent');
                findingsContent.innerHTML = '';
                if (data.keyFindings && data.keyFindings.length > 0) {
                    data.keyFindings.forEach(finding => {
                        const div = document.createElement('div');
                        div.className = 'key-finding';
                        div.innerHTML = finding;
                        findingsContent.appendChild(div);
                    });
                } else {
                    findingsContent.innerHTML = '<p class="text-muted">暂无关键发现</p>';
                }

                // 参考资料
                const referencesContent = document.getElementById('referencesContent');
                referencesContent.innerHTML = '';
                if (data.references && data.references.length > 0) {
                    data.references.forEach(ref => {
                        const div = document.createElement('div');
                        div.className = 'reference-item';
                        div.innerHTML = `
                            <h6>${ref.title}</h6>
                            <p><strong>来源：</strong> ${ref.source || '-'}</p>
                            ${ref.url ? `<p><a href="${ref.url}" target="_blank">查看原文</a></p>` : ''}
                            ${ref.summary ? `<p>${ref.summary}</p>` : ''}
                        `;
                        referencesContent.appendChild(div);
                    });
                } else {
                    referencesContent.innerHTML = '<p class="text-muted">暂无参考资料</p>';
                }

                // 加载研究图
                if (data.graph) {
                    loadResearchGraph(data.graph);
                }
            })
            .catch(error => {
                console.error('Error loading research result:', error);
                alert('加载研究结果失败: ' + error);
            });
    }

    // 加载研究图
    function loadResearchGraph(graph) {
        researchGraph = graph;

        // 准备节点和边数据
        const nodes = [];
        const edges = [];

        // 添加节点
        Object.values(graph.nodes).forEach(node => {
            let color = '#aaa'; // 默认颜色

            // 根据节点状态设置颜色
            if (node.status === 'COMPLETED') {
                color = '#28a745'; // 绿色
            } else if (node.status === 'RUNNING') {
                color = '#007bff'; // 蓝色
            } else if (node.status === 'FAILED') {
                color = '#dc3545'; // 红色
            } else if (node.status === 'PENDING') {
                color = '#6c757d'; // 灰色
            }

            nodes.push({
                id: node.nodeId,
                label: node.title,
                title: node.description,
                color: {
                    background: color,
                    border: '#666',
                    highlight: {
                        background: '#f8f9fa',
                        border: '#343a40'
                    }
                },
                font: {
                    color: '#fff'
                }
            });
        });

        // 添加边
        Object.entries(graph.edges).forEach(([fromId, toIds]) => {
            toIds.forEach(toId => {
                edges.push({
                    from: fromId,
                    to: toId,
                    arrows: 'to'
                });
            });
        });

        // 创建网络图
        const container = document.getElementById('graphContainer');
        const data = {
            nodes: new vis.DataSet(nodes),
            edges: new vis.DataSet(edges)
        };
        const options = {
            layout: {
                hierarchical: {
                    direction: 'UD',
                    sortMethod: 'directed',
                    levelSeparation: 150
                }
            },
            physics: {
                hierarchicalRepulsion: {
                    nodeDistance: 150
                }
            },
            interaction: {
                navigationButtons: true,
                keyboard: true
            }
        };

        network = new vis.Network(container, data, options);

        // 点击节点显示详情
        network.on('click', function(params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = graph.nodes[nodeId];
                if (node) {
                    showNodeDetails(node);
                }
            }
        });
    }

    // 显示节点详情
    function showNodeDetails(node) {
        const nodeDetails = document.getElementById('nodeDetails');

        let statusText = '未知';
        switch (node.status) {
            case 'PENDING': statusText = '待处理'; break;
            case 'RUNNING': statusText = '处理中'; break;
            case 'COMPLETED': statusText = '已完成'; break;
            case 'FAILED': statusText = '失败'; break;
            case 'SKIPPED': statusText = '已跳过'; break;
        }

        let html = `
                <h5>${node.title}</h5>
                <p><strong>描述：</strong> ${node.description}</p>
                <p><strong>类型：</strong> ${getNodeTypeText(node.nodeType)}</p>
                <p><strong>状态：</strong> ${statusText}</p>
                <p><strong>深度：</strong> ${node.depth || '-'}</p>
                <p><strong>权重：</strong> ${node.weight || '-'}</p>
            `;

        if (node.startTime) {
            html += `<p><strong>开始时间：</strong> ${new Date(node.startTime).toLocaleString()}</p>`;
        }

        if (node.endTime) {
            html += `<p><strong>结束时间：</strong> ${new Date(node.endTime).toLocaleString()}</p>`;
        }

        if (node.duration) {
            html += `<p><strong>耗时：</strong> ${formatDuration(node.duration)}</p>`;
        }

        if (node.content) {
            html += `<hr><h6>节点内容：</h6><div class="mt-2">${formatText(node.content)}</div>`;
        }

        nodeDetails.innerHTML = html;
    }

    // 获取节点类型文本
    function getNodeTypeText(nodeType) {
        switch (nodeType) {
            case 'ROOT': return '根节点';
            case 'DECOMPOSITION': return '问题分解';
            case 'INFORMATION_GATHERING': return '信息收集';
            case 'ANALYSIS': return '分析';
            case 'VALIDATION': return '验证';
            case 'SYNTHESIS': return '综合';
            case 'CONCLUSION': return '结论';
            default: return nodeType;
        }
    }

    // 取消研究
    function cancelResearch() {
        if (!currentResearchId) return;

        if (confirm('确定要取消当前研究吗？')) {
            fetch(`/api/deepresearch/cancel/${currentResearchId}`, {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    alert('研究已取消');
                    clearInterval(progressInterval);
                    loadResearchResult(currentResearchId);
                })
                .catch(error => {
                    console.error('Error cancelling research:', error);
                    alert('取消研究失败: ' + error);
                });
        }
    }

    // 加载历史研究列表
    function loadResearchList() {
        // 这里需要后端提供一个API来获取历史研究列表
        // 由于示例中没有这个API，这里只是模拟一下
        // 实际应用中应该调用后端API

        // 模拟数据
        const mockData = [
            {
                researchId: 'research_example_1',
                topic: '人工智能在医疗领域的应用',
                status: 'COMPLETED',
                startTime: '2023-06-15 10:30:45'
            },
            {
                researchId: 'research_example_2',
                topic: '区块链技术发展趋势',
                status: 'COMPLETED',
                startTime: '2023-06-14 15:20:10'
            }
        ];

        const listBody = document.getElementById('researchListBody');
        listBody.innerHTML = '';

        if (mockData.length === 0) {
            listBody.innerHTML = '<tr><td colspan="5" class="text-center">暂无研究记录</td></tr>';
            return;
        }

        mockData.forEach(item => {
            addToResearchList(item);
        });
    }

    // 添加到研究列表
    function addToResearchList(item) {
        const listBody = document.getElementById('researchListBody');

        // 如果是第一条记录，清空"暂无记录"提示
        if (listBody.innerHTML.includes('暂无研究记录')) {
            listBody.innerHTML = '';
        }

        // 检查是否已存在
        const existingRow = document.getElementById(`research-row-${item.researchId}`);
        if (existingRow) {
            // 更新状态
            const statusCell = existingRow.querySelector('.research-status');
            statusCell.textContent = getStatusText(item.status);
            statusCell.className = `research-status ${getStatusClass(item.status)}`;
            return;
        }

        // 创建新行
        const row = document.createElement('tr');
        row.id = `research-row-${item.researchId}`;

        row.innerHTML = `
                <td>${item.researchId}</td>
                <td>${item.topic}</td>
                <td class="research-status ${getStatusClass(item.status)}">${getStatusText(item.status)}</td>
                <td>${item.startTime}</td>
                <td>
                    <button class="btn btn-sm btn-primary view-result" data-id="${item.researchId}">查看结果</button>
                </td>
            `;

        // 添加到列表顶部
        if (listBody.firstChild) {
            listBody.insertBefore(row, listBody.firstChild);
        } else {
            listBody.appendChild(row);
        }

        // 绑定查看结果事件
        row.querySelector('.view-result').addEventListener('click', function() {
            const researchId = this.getAttribute('data-id');
