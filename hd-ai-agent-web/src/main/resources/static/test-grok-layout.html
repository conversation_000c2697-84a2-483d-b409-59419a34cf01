<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Grok风格对话框布局测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
      color: var(--text-primary);
    }

    .chat-container {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      min-height: 600px;
    }

    .messages-area {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      max-width: 100%;
    }

    /* Grok风格的消息气泡样式 */
    .message-bubble {
      border-radius: var(--radius-xl);
      padding: 1.25rem 1.5rem;
      margin-bottom: 1.5rem;
      line-height: 1.7;
      backdrop-filter: blur(10px);
      border: 1px solid var(--border-primary);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }
    
    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
      max-width: 75%; /* 用户消息保持较小宽度 */
    }
    
    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-secondary);
      width: 100%; /* AI消息使用全宽 */
      max-width: none; /* 移除最大宽度限制 */
    }

    /* 头像样式 */
    .message-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    .message-name {
      font-weight: 600;
      font-size: 0.875rem;
    }

    .message-time {
      font-size: 0.75rem;
      color: var(--text-tertiary);
    }

    /* Markdown 内容样式 */
    .markdown-content {
      line-height: 1.7;
    }

    .markdown-content p {
      margin-bottom: 1rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }

    .markdown-content li {
      margin-bottom: 0.5rem;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
      border: 1px solid var(--border-primary);
    }

    .markdown-content pre {
      background: var(--bg-secondary);
      padding: 1rem;
      border-radius: var(--radius-md);
      overflow-x: auto;
      margin: 1rem 0;
      border: 1px solid var(--border-primary);
      box-shadow: var(--shadow-sm);
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      border: none;
      color: var(--text-primary);
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
      .user-message {
        max-width: 90%; /* 移动端用户消息稍微增大 */
      }
      
      .assistant-message {
        width: 100%; /* 移动端AI消息仍然全宽 */
        padding: 1rem 1.25rem; /* 移动端稍微减少内边距 */
      }
      
      .message-bubble {
        padding: 0.875rem 1rem;
      }
      
      .avatar {
        width: 28px;
        height: 28px;
        font-size: 12px;
      }
    }

    /* 大屏幕优化 - 为AI消息提供更好的阅读体验 */
    @media (min-width: 1200px) {
      .assistant-message {
        padding: 1.5rem 2rem; /* 大屏幕增加内边距 */
      }
      
      .markdown-content {
        font-size: 1rem; /* 稍微增大字体 */
        line-height: 1.8; /* 增加行高 */
      }
    }

    .description {
      color: var(--text-tertiary);
      line-height: 1.6;
      margin-bottom: 2rem;
      text-align: center;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">Grok风格对话框布局</h1>
    
    <p class="description">
      参考 Grok.com 的设计，AI消息使用全宽布局以更好地展示长文本内容，用户消息保持较小宽度以区分对话角色。
    </p>
    
    <div class="chat-container">
      <div class="messages-area">
        <!-- 用户消息示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <div>
              <div class="message-name">你</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble user-message">
            请帮我分析一下这个复杂的数据库性能问题，包括查询优化、索引策略和架构建议。
          </div>
        </div>

        <!-- AI消息示例 - 展示长文本内容 -->
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <div>
              <div class="message-name">秒答</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble assistant-message">
            <div class="markdown-content">
              <p>我很乐意帮您分析数据库性能问题。这是一个复杂的主题，我将从多个维度为您提供详细的分析和建议：</p>
              
              <h3><strong>1. 查询优化策略</strong></h3>
              <ul>
                <li><strong>SQL语句优化</strong>：避免使用 <code>SELECT *</code>，只查询需要的字段</li>
                <li><strong>WHERE条件优化</strong>：将选择性高的条件放在前面，使用合适的数据类型</li>
                <li><strong>JOIN优化</strong>：选择合适的JOIN类型，避免笛卡尔积</li>
                <li><strong>子查询优化</strong>：考虑将相关子查询改写为JOIN</li>
              </ul>

              <h3><strong>2. 索引策略</strong></h3>
              <ul>
                <li><strong>单列索引</strong>：为经常用于WHERE、ORDER BY的列创建索引</li>
                <li><strong>复合索引</strong>：根据查询模式创建多列索引，注意列的顺序</li>
                <li><strong>覆盖索引</strong>：让索引包含查询所需的所有列，避免回表</li>
                <li><strong>索引维护</strong>：定期分析索引使用情况，删除无用索引</li>
              </ul>

              <h3><strong>3. 架构优化建议</strong></h3>
              <ul>
                <li><strong>读写分离</strong>：使用主从复制，读操作分散到从库</li>
                <li><strong>分库分表</strong>：对于大数据量场景，考虑水平分片</li>
                <li><strong>缓存策略</strong>：引入Redis等缓存层，减少数据库压力</li>
                <li><strong>连接池优化</strong>：合理配置连接池参数，避免连接泄漏</li>
              </ul>

              <p>为了提供更精确的建议，建议您提供以下信息：</p>
              <ul>
                <li>数据库类型和版本</li>
                <li>具体的性能问题表现</li>
                <li>数据量规模和增长趋势</li>
                <li>当前的硬件配置</li>
              </ul>

              <p>这样我就能为您制定更有针对性的优化方案。</p>
            </div>
          </div>
        </div>

        <!-- 用户回复示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <div>
              <div class="message-name">你</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble user-message">
            非常详细的分析！我使用的是MySQL 8.0，主要问题是查询响应时间过长。
          </div>
        </div>

        <!-- AI回复示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <div>
              <div class="message-name">秒答</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble assistant-message">
            <div class="markdown-content">
              <p>很好！MySQL 8.0 有很多性能优化特性。针对查询响应时间过长的问题，我建议按以下步骤进行诊断：</p>
              
              <h3><strong>立即可以做的诊断</strong></h3>
              <pre><code>-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;

-- 查看当前运行的查询
SHOW PROCESSLIST;

-- 分析具体查询的执行计划
EXPLAIN FORMAT=JSON SELECT ...;</code></pre>

              <p>这些命令可以帮助我们快速定位性能瓶颈。您可以先运行这些命令，然后把结果分享给我进行进一步分析。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>
</html>
