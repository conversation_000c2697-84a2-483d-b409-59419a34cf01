<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>联网搜索功能测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
      color: var(--text-primary);
    }

    .demo-section {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    /* 联网搜索按钮样式 */
    .web-search-toggle {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
      color: var(--text-secondary);
      margin-bottom: 1rem;
      user-select: none;
    }

    .web-search-toggle:hover {
      border-color: var(--accent-primary);
      background: var(--accent-light);
    }

    .web-search-toggle.active {
      border-color: var(--accent-primary);
      background: var(--accent-light);
      color: var(--accent-primary);
    }

    .web-search-toggle .toggle-switch {
      position: relative;
      width: 36px;
      height: 20px;
      background: var(--border-primary);
      border-radius: 10px;
      transition: all 0.2s ease;
    }

    .web-search-toggle.active .toggle-switch {
      background: var(--accent-primary);
    }

    .web-search-toggle .toggle-switch::after {
      content: '';
      position: absolute;
      top: 2px;
      left: 2px;
      width: 16px;
      height: 16px;
      background: white;
      border-radius: 50%;
      transition: all 0.2s ease;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .web-search-toggle.active .toggle-switch::after {
      transform: translateX(16px);
    }

    .web-search-icon {
      width: 16px;
      height: 16px;
      opacity: 0.7;
    }

    .web-search-text {
      font-weight: 500;
    }

    .web-search-status {
      font-size: 0.75rem;
      opacity: 0.8;
    }

    .status-display {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      padding: 1rem;
      margin-top: 1rem;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--border-primary);
    }

    .status-item:last-child {
      border-bottom: none;
    }

    .status-label {
      font-weight: 500;
      color: var(--text-secondary);
    }

    .status-value {
      color: var(--text-primary);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }

    .status-value.active {
      color: var(--accent-primary);
    }

    .test-button {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-md);
      width: 100%;
      margin-top: 1rem;
    }

    .test-button:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .description {
      color: var(--text-tertiary);
      line-height: 1.6;
      margin-bottom: 1rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">联网搜索功能测试</h1>
    
    <div class="demo-section">
      <h2 class="section-title">联网搜索切换</h2>
      <p class="description">
        点击下方的联网搜索按钮来切换功能状态。状态会自动保存到本地存储中，页面刷新后会保持之前的设置。
      </p>
      
      <!-- 联网搜索切换按钮 -->
      <div class="web-search-toggle" id="webSearchToggle">
        <svg xmlns="http://www.w3.org/2000/svg" class="web-search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c-1.657 0-3-4.03-3-9s1.343-9 3-9m0 9c1.657 0 3 4.03 3 9s-1.343 9-3 9m-9 9a9 9 0 019-9" />
        </svg>
        <div class="flex flex-col">
          <span class="web-search-text">联网搜索</span>
          <span class="web-search-status" id="webSearchStatus">已关闭</span>
        </div>
        <div class="toggle-switch"></div>
      </div>

      <div class="status-display">
        <div class="status-item">
          <span class="status-label">当前状态:</span>
          <span class="status-value" id="currentStatus">false</span>
        </div>
        <div class="status-item">
          <span class="status-label">本地存储:</span>
          <span class="status-value" id="localStorageValue">null</span>
        </div>
        <div class="status-item">
          <span class="status-label">发送数据:</span>
          <span class="status-value" id="sendData">{"webSearch": false}</span>
        </div>
      </div>

      <button class="test-button" onclick="simulateSend()">
        模拟发送消息 (查看控制台)
      </button>
    </div>

    <div class="demo-section">
      <h2 class="section-title">功能说明</h2>
      <div class="description">
        <p><strong>功能特点:</strong></p>
        <ul style="margin: 1rem 0; padding-left: 1.5rem; color: var(--text-secondary);">
          <li>优雅的切换动画效果</li>
          <li>状态持久化保存</li>
          <li>实时状态反馈</li>
          <li>与发送消息接口集成</li>
          <li>响应式设计，支持移动端</li>
        </ul>
        
        <p><strong>技术实现:</strong></p>
        <ul style="margin: 1rem 0; padding-left: 1.5rem; color: var(--text-secondary);">
          <li>使用 localStorage 保存状态</li>
          <li>CSS 变量实现主题一致性</li>
          <li>平滑的过渡动画</li>
          <li>事件驱动的状态管理</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // 联网搜索状态
    const webSearchState = {
      enabled: false
    };

    // DOM元素
    const elements = {
      webSearchToggle: document.getElementById('webSearchToggle'),
      webSearchStatus: document.getElementById('webSearchStatus'),
      currentStatus: document.getElementById('currentStatus'),
      localStorageValue: document.getElementById('localStorageValue'),
      sendData: document.getElementById('sendData')
    };

    // 初始化
    function init() {
      // 加载保存的状态
      const savedState = localStorage.getItem('webSearchEnabled');
      if (savedState !== null) {
        webSearchState.enabled = JSON.parse(savedState);
      }
      
      updateUI();
      setupEventListeners();
    }

    // 设置事件监听器
    function setupEventListeners() {
      elements.webSearchToggle.addEventListener('click', toggleWebSearch);
    }

    // 切换联网搜索状态
    function toggleWebSearch() {
      webSearchState.enabled = !webSearchState.enabled;
      updateUI();
      saveState();
    }

    // 更新UI
    function updateUI() {
      elements.webSearchToggle.classList.toggle('active', webSearchState.enabled);
      elements.webSearchStatus.textContent = webSearchState.enabled ? '已开启' : '已关闭';
      
      // 更新状态显示
      elements.currentStatus.textContent = webSearchState.enabled.toString();
      elements.currentStatus.classList.toggle('active', webSearchState.enabled);
      
      elements.localStorageValue.textContent = localStorage.getItem('webSearchEnabled') || 'null';
      
      elements.sendData.textContent = JSON.stringify({
        webSearch: webSearchState.enabled
      });
    }

    // 保存状态
    function saveState() {
      localStorage.setItem('webSearchEnabled', JSON.stringify(webSearchState.enabled));
      updateUI();
    }

    // 模拟发送消息
    function simulateSend() {
      const messageData = {
        chatId: 'test-chat-id',
        message: '这是一条测试消息',
        stream: true,
        tool: '',
        webSearch: webSearchState.enabled
      };
      
      console.log('模拟发送消息数据:', messageData);
      console.log('联网搜索状态:', webSearchState.enabled ? '已启用' : '已禁用');
      
      alert(`消息发送模拟完成！\n联网搜索: ${webSearchState.enabled ? '已启用' : '已禁用'}\n请查看控制台获取详细信息。`);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>
