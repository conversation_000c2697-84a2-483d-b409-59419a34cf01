<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>自然边框AI对话框测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .demo-section {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 1.5rem;
    }

    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
      text-align: center;
    }

    .messages-area {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    /* 消息气泡基础样式 */
    .message-bubble {
      border-radius: var(--radius-xl);
      padding: 1.25rem 1.5rem;
      line-height: 1.7;
      backdrop-filter: blur(10px);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }
    
    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
      max-width: 75%;
      border: 1px solid transparent;
    }
    
    /* 原始边框样式 */
    .assistant-message-old {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-secondary); /* 原始边框 */
      width: 100%;
      max-width: none;
    }

    /* 弱化边框样式 */
    .assistant-message-new {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: 1px solid rgba(42, 42, 42, 0.3); /* 弱化边框，使用半透明 */
      width: 100%;
      max-width: none;
    }

    /* 头像样式 */
    .message-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .avatar {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    .message-name {
      font-weight: 600;
      font-size: 0.75rem;
    }

    .markdown-content {
      line-height: 1.6;
    }

    .markdown-content p {
      margin-bottom: 0.75rem;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
    }

    .feature-highlight {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-md);
      padding: 1rem;
      margin-top: 2rem;
    }

    .feature-list {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
      color: var(--text-secondary);
    }

    .feature-list li {
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .comparison {
        grid-template-columns: 1fr;
      }
      
      .user-message {
        max-width: 90%;
      }
      
      .message-bubble {
        padding: 1rem 1.25rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">自然边框AI对话框</h1>
    <p class="subtitle">
      通过弱化AI对话框的边框，让对话界面看起来更加自然和融入，减少视觉干扰，提升阅读体验。
    </p>
    
    <div class="comparison">
      <!-- 原始边框效果 -->
      <div class="demo-section">
        <h2 class="section-title">原始边框</h2>
        <div class="messages-area">
          <div style="display: flex; flex-direction: column; align-items: flex-end;">
            <div class="message-header" style="flex-direction: row-reverse;">
              <div class="avatar user-avatar">👤</div>
              <div class="message-name">你</div>
            </div>
            <div class="message-bubble user-message">
              请解释一下什么是机器学习？
            </div>
          </div>

          <div style="display: flex; flex-direction: column; align-items: flex-start;">
            <div class="message-header">
              <div class="avatar assistant-avatar">🤖</div>
              <div class="message-name">AI助手</div>
            </div>
            <div class="message-bubble assistant-message-old">
              <div class="markdown-content">
                <p>机器学习是人工智能的一个重要分支，它让计算机能够在没有明确编程的情况下学习和改进。</p>
                <p><strong>核心概念：</strong></p>
                <p>• <strong>监督学习</strong>：使用标记数据训练模型</p>
                <p>• <strong>无监督学习</strong>：从未标记数据中发现模式</p>
                <p>• <strong>强化学习</strong>：通过奖励机制学习最优策略</p>
                <p>机器学习广泛应用于图像识别、自然语言处理、推荐系统等领域。</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 弱化边框效果 -->
      <div class="demo-section">
        <h2 class="section-title">弱化边框 ✨</h2>
        <div class="messages-area">
          <div style="display: flex; flex-direction: column; align-items: flex-end;">
            <div class="message-header" style="flex-direction: row-reverse;">
              <div class="avatar user-avatar">👤</div>
              <div class="message-name">你</div>
            </div>
            <div class="message-bubble user-message">
              请解释一下什么是机器学习？
            </div>
          </div>

          <div style="display: flex; flex-direction: column; align-items: flex-start;">
            <div class="message-header">
              <div class="avatar assistant-avatar">🤖</div>
              <div class="message-name">AI助手</div>
            </div>
            <div class="message-bubble assistant-message-new">
              <div class="markdown-content">
                <p>机器学习是人工智能的一个重要分支，它让计算机能够在没有明确编程的情况下学习和改进。</p>
                <p><strong>核心概念：</strong></p>
                <p>• <strong>监督学习</strong>：使用标记数据训练模型</p>
                <p>• <strong>无监督学习</strong>：从未标记数据中发现模式</p>
                <p>• <strong>强化学习</strong>：通过奖励机制学习最优策略</p>
                <p>机器学习广泛应用于图像识别、自然语言处理、推荐系统等领域。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="feature-highlight">
      <h3 style="margin-top: 0; color: var(--text-primary);">优化效果对比</h3>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <div>
          <h4 style="color: var(--text-secondary); margin-bottom: 0.5rem;">原始边框</h4>
          <ul class="feature-list">
            <li>边框较为明显</li>
            <li>视觉边界清晰</li>
            <li>可能产生视觉干扰</li>
            <li>对话框显得较为突兀</li>
          </ul>
        </div>
        <div>
          <h4 style="color: var(--accent-primary); margin-bottom: 0.5rem;">弱化边框 ✨</h4>
          <ul class="feature-list">
            <li>边框更加自然融入</li>
            <li>减少视觉干扰</li>
            <li>提升阅读体验</li>
            <li>保持必要的视觉分离</li>
          </ul>
        </div>
      </div>
      
      <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--border-primary);">
        <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
          <strong>技术实现：</strong> 使用 <code>rgba(42, 42, 42, 0.3)</code> 替代 <code>var(--border-secondary)</code>，
          通过半透明效果让边框更自然地融入背景。
        </p>
      </div>
    </div>
  </div>
</body>
</html>
