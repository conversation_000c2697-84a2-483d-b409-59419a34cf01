<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>宽屏表格显示测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 1400px; /* 更大的容器宽度 */
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .comparison {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-bottom: 2rem;
    }

    .demo-section {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 1.5rem;
    }

    .section-title {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
      text-align: center;
    }

    .chat-container {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .messages-area {
      max-width: 1200px; /* 对应 max-w-6xl */
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    .messages-area-narrow {
      max-width: 768px; /* 对应 max-w-3xl */
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
    }

    /* 消息气泡样式 */
    .message-bubble {
      border-radius: var(--radius-xl);
      padding: 1.25rem 1.5rem;
      line-height: 1.7;
      backdrop-filter: blur(10px);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }
    
    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-primary);
      max-width: 75%;
    }
    
    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: none;
      width: 100%;
      max-width: none;
    }

    /* 头像样式 */
    .message-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    .message-name {
      font-weight: 600;
      font-size: 0.875rem;
    }

    /* Markdown 表格样式 */
    .markdown-content {
      line-height: 1.7;
    }

    .markdown-content p {
      margin-bottom: 1rem;
    }

    .markdown-content table {
      border-collapse: collapse;
      margin: 1rem 0;
      width: 100%;
      border-radius: var(--radius-md);
      overflow: hidden;
      border: 1px solid var(--border-primary);
      font-size: 0.9rem;
    }
    
    .markdown-content th,
    .markdown-content td {
      border: 1px solid var(--border-primary);
      padding: 0.75rem 1rem;
      text-align: left;
      vertical-align: top;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
    
    .markdown-content th {
      background: var(--bg-secondary);
      font-weight: 600;
      color: var(--text-primary);
      white-space: nowrap;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .feature-highlight {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-md);
      padding: 1.5rem;
      margin-top: 2rem;
    }

    @media (max-width: 768px) {
      .comparison {
        grid-template-columns: 1fr;
      }
      
      .user-message {
        max-width: 90%;
      }
      
      .message-bubble {
        padding: 1rem 1.25rem;
      }

      .messages-area,
      .messages-area-narrow {
        max-width: 100%;
        padding: 0 1rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">宽屏表格显示优化</h1>
    <p class="subtitle">
      将AI对话框的最大宽度从 max-w-4xl (896px) 扩大到 max-w-6xl (1152px)，为表格和复杂内容提供更多展示空间。
    </p>
    
    <div class="comparison">
      <!-- 原始宽度 -->
      <div class="demo-section">
        <h2 class="section-title">原始宽度 (max-w-3xl)</h2>
        <div class="messages-area-narrow">
          <div style="display: flex; flex-direction: column; align-items: flex-start;">
            <div class="message-header">
              <div class="avatar assistant-avatar">🤖</div>
              <div class="message-name">AI助手</div>
            </div>
            <div class="message-bubble assistant-message">
              <div class="markdown-content">
                <p><strong>数据库性能对比表：</strong></p>
                <table>
                  <thead>
                    <tr>
                      <th>数据库</th>
                      <th>查询速度</th>
                      <th>并发处理</th>
                      <th>内存使用</th>
                      <th>适用场景</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>MySQL</td>
                      <td>中等</td>
                      <td>良好</td>
                      <td>适中</td>
                      <td>Web应用</td>
                    </tr>
                    <tr>
                      <td>PostgreSQL</td>
                      <td>快速</td>
                      <td>优秀</td>
                      <td>较高</td>
                      <td>复杂查询</td>
                    </tr>
                    <tr>
                      <td>Redis</td>
                      <td>极快</td>
                      <td>优秀</td>
                      <td>高</td>
                      <td>缓存系统</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 扩大宽度 -->
      <div class="demo-section">
        <h2 class="section-title">扩大宽度 (max-w-6xl) ✨</h2>
        <div class="messages-area">
          <div style="display: flex; flex-direction: column; align-items: flex-start;">
            <div class="message-header">
              <div class="avatar assistant-avatar">🤖</div>
              <div class="message-name">AI助手</div>
            </div>
            <div class="message-bubble assistant-message">
              <div class="markdown-content">
                <p><strong>数据库性能对比表：</strong></p>
                <table>
                  <thead>
                    <tr>
                      <th>数据库</th>
                      <th>查询速度</th>
                      <th>并发处理</th>
                      <th>内存使用</th>
                      <th>适用场景</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>MySQL</td>
                      <td>中等</td>
                      <td>良好</td>
                      <td>适中</td>
                      <td>Web应用</td>
                    </tr>
                    <tr>
                      <td>PostgreSQL</td>
                      <td>快速</td>
                      <td>优秀</td>
                      <td>较高</td>
                      <td>复杂查询</td>
                    </tr>
                    <tr>
                      <td>Redis</td>
                      <td>极快</td>
                      <td>优秀</td>
                      <td>高</td>
                      <td>缓存系统</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 复杂表格示例 -->
    <div class="chat-container">
      <h2 style="margin-top: 0; margin-bottom: 1.5rem; text-align: center;">复杂表格展示效果</h2>
      <div class="messages-area">
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <div class="message-name">你</div>
          </div>
          <div class="message-bubble user-message">
            请提供一个详细的前端框架对比表格。
          </div>
        </div>

        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <div class="message-name">AI助手</div>
          </div>
          <div class="message-bubble assistant-message">
            <div class="markdown-content">
              <p>以下是主流前端框架的详细对比：</p>
              
              <table>
                <thead>
                  <tr>
                    <th>框架</th>
                    <th>开发公司</th>
                    <th>首次发布</th>
                    <th>学习难度</th>
                    <th>性能表现</th>
                    <th>生态系统</th>
                    <th>适用项目规模</th>
                    <th>主要特点</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td><strong>React</strong></td>
                    <td>Facebook</td>
                    <td>2013年</td>
                    <td>中等</td>
                    <td>优秀</td>
                    <td>非常丰富</td>
                    <td>中大型</td>
                    <td>虚拟DOM、组件化、单向数据流</td>
                  </tr>
                  <tr>
                    <td><strong>Vue.js</strong></td>
                    <td>尤雨溪</td>
                    <td>2014年</td>
                    <td>简单</td>
                    <td>优秀</td>
                    <td>丰富</td>
                    <td>小中大型</td>
                    <td>渐进式、双向绑定、模板语法</td>
                  </tr>
                  <tr>
                    <td><strong>Angular</strong></td>
                    <td>Google</td>
                    <td>2016年</td>
                    <td>困难</td>
                    <td>良好</td>
                    <td>完整</td>
                    <td>大型企业</td>
                    <td>TypeScript、依赖注入、全功能框架</td>
                  </tr>
                  <tr>
                    <td><strong>Svelte</strong></td>
                    <td>Rich Harris</td>
                    <td>2016年</td>
                    <td>简单</td>
                    <td>极佳</td>
                    <td>发展中</td>
                    <td>小中型</td>
                    <td>编译时优化、无虚拟DOM、轻量级</td>
                  </tr>
                </tbody>
              </table>

              <p><strong>选择建议：</strong></p>
              <ul>
                <li><strong>React</strong>：适合有一定经验的团队，生态丰富，就业机会多</li>
                <li><strong>Vue.js</strong>：适合快速开发，学习曲线平缓，中文文档完善</li>
                <li><strong>Angular</strong>：适合大型企业项目，需要完整的解决方案</li>
                <li><strong>Svelte</strong>：适合追求性能和包体积的项目</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="feature-highlight">
      <h3 style="margin-top: 0; color: var(--text-primary);">宽度优化效果</h3>
      <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <div>
          <h4 style="color: var(--text-secondary); margin-bottom: 0.5rem;">原始宽度 (768px)</h4>
          <ul style="margin: 0.5rem 0; padding-left: 1.5rem; color: var(--text-secondary);">
            <li>表格列较窄，内容可能换行</li>
            <li>复杂表格显示受限</li>
            <li>适合简单对话内容</li>
          </ul>
        </div>
        <div>
          <h4 style="color: var(--accent-primary); margin-bottom: 0.5rem;">扩大宽度 (1152px) ✨</h4>
          <ul style="margin: 0.5rem 0; padding-left: 1.5rem; color: var(--text-secondary);">
            <li>表格有更多水平空间</li>
            <li>复杂数据展示更清晰</li>
            <li>减少不必要的换行</li>
            <li>提升表格类内容的可读性</li>
          </ul>
        </div>
      </div>
      
      <div style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--border-primary);">
        <p style="margin: 0; color: var(--text-secondary); font-size: 0.875rem;">
          <strong>技术实现：</strong> 将消息容器的最大宽度从 <code>max-w-4xl</code> (896px) 扩大到 <code>max-w-6xl</code> (1152px)，
          增加了 256px 的水平空间，特别适合展示表格、代码和结构化数据。
        </p>
      </div>
    </div>
  </div>
</body>
</html>
