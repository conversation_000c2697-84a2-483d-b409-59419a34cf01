<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>无边框AI对话框测试</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    .subtitle {
      text-align: center;
      color: var(--text-secondary);
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .chat-container {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      min-height: 600px;
    }

    .messages-area {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      max-width: 100%;
    }

    /* 消息气泡基础样式 */
    .message-bubble {
      border-radius: var(--radius-xl);
      padding: 1.25rem 1.5rem;
      line-height: 1.7;
      backdrop-filter: blur(10px);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }
    
    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
      border: 1px solid var(--border-primary); /* 用户消息保留边框 */
      max-width: 75%;
    }
    
    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: none; /* 完全取消AI消息的边框 */
      width: 100%;
      max-width: none;
    }

    /* 头像样式 */
    .message-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    .message-name {
      font-weight: 600;
      font-size: 0.875rem;
    }

    .message-time {
      font-size: 0.75rem;
      color: var(--text-tertiary);
    }

    /* Markdown 内容样式 */
    .markdown-content {
      line-height: 1.7;
    }

    .markdown-content p {
      margin-bottom: 1rem;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }

    .markdown-content ul, .markdown-content ol {
      margin: 1rem 0;
      padding-left: 1.5rem;
    }

    .markdown-content li {
      margin-bottom: 0.5rem;
    }

    .markdown-content strong {
      font-weight: 600;
      color: var(--text-primary);
    }

    .markdown-content code {
      background: var(--bg-secondary);
      color: var(--accent-primary);
      padding: 0.125rem 0.375rem;
      border-radius: var(--radius-sm);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 0.875em;
      border: 1px solid var(--border-primary);
    }

    .markdown-content pre {
      background: var(--bg-secondary);
      padding: 1rem;
      border-radius: var(--radius-md);
      overflow-x: auto;
      margin: 1rem 0;
      border: 1px solid var(--border-primary);
      box-shadow: var(--shadow-sm);
    }

    .markdown-content pre code {
      background: transparent;
      padding: 0;
      border: none;
      color: var(--text-primary);
    }

    .feature-highlight {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-md);
      padding: 1.5rem;
      margin-top: 2rem;
    }

    .feature-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
      margin-top: 1rem;
    }

    .feature-list {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
      color: var(--text-secondary);
    }

    .feature-list li {
      margin-bottom: 0.5rem;
    }

    .highlight-box {
      background: var(--accent-light);
      border: 1px solid var(--accent-primary);
      border-radius: var(--radius-md);
      padding: 1rem;
      margin-top: 1rem;
    }

    @media (max-width: 768px) {
      .feature-grid {
        grid-template-columns: 1fr;
      }
      
      .user-message {
        max-width: 90%;
      }
      
      .message-bubble {
        padding: 1rem 1.25rem;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">无边框AI对话框</h1>
    <p class="subtitle">
      完全取消AI对话框的边框，让AI回复更自然地融入背景，同时保留用户消息的边框以维持对话的层次感。
    </p>
    
    <div class="chat-container">
      <div class="messages-area">
        <!-- 用户消息示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <div>
              <div class="message-name">你</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble user-message">
            请详细介绍一下React Hooks的使用方法和最佳实践。
          </div>
        </div>

        <!-- AI消息示例 - 无边框 -->
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <div>
              <div class="message-name">秒答</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble assistant-message">
            <div class="markdown-content">
              <p>React Hooks 是 React 16.8 引入的重要特性，让你可以在函数组件中使用状态和其他 React 特性。以下是详细的使用方法和最佳实践：</p>
              
              <p><strong>1. 基础 Hooks</strong></p>
              <ul>
                <li><strong>useState</strong>：管理组件状态</li>
                <li><strong>useEffect</strong>：处理副作用</li>
                <li><strong>useContext</strong>：访问 React Context</li>
              </ul>

              <p><strong>2. useState 使用示例</strong></p>
              <pre><code>import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);
  
  return (
    &lt;div&gt;
      &lt;p&gt;Count: {count}&lt;/p&gt;
      &lt;button onClick={() =&gt; setCount(count + 1)}&gt;
        Increment
      &lt;/button&gt;
    &lt;/div&gt;
  );
}</code></pre>

              <p><strong>3. useEffect 最佳实践</strong></p>
              <ul>
                <li>总是在依赖数组中包含所有使用的变量</li>
                <li>使用多个 useEffect 分离不同的关注点</li>
                <li>记得清理副作用以避免内存泄漏</li>
              </ul>

              <p><strong>4. 自定义 Hooks</strong></p>
              <p>创建可复用的逻辑，遵循 "use" 命名约定：</p>
              <pre><code>function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() =&gt; {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setValue = (value) =&gt; {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(error);
    }
  };

  return [storedValue, setValue];
}</code></pre>

              <p>这些是 React Hooks 的核心概念和实践。需要更深入了解某个特定的 Hook 吗？</p>
            </div>
          </div>
        </div>

        <!-- 用户回复示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <div>
              <div class="message-name">你</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble user-message">
            很详细！能再讲讲 useEffect 的依赖数组吗？
          </div>
        </div>

        <!-- AI回复示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <div>
              <div class="message-name">秒答</div>
              <div class="message-time">刚刚</div>
            </div>
          </div>
          <div class="message-bubble assistant-message">
            <div class="markdown-content">
              <p>当然！useEffect 的依赖数组是一个非常重要的概念：</p>
              
              <p><strong>依赖数组的作用</strong></p>
              <ul>
                <li>控制 effect 何时重新执行</li>
                <li>优化性能，避免不必要的重新渲染</li>
                <li>确保 effect 中使用的值是最新的</li>
              </ul>

              <p><strong>三种情况：</strong></p>
              <pre><code>// 1. 没有依赖数组 - 每次渲染都执行
useEffect(() =&gt; {
  console.log('每次渲染都执行');
});

// 2. 空依赖数组 - 只在挂载时执行一次
useEffect(() =&gt; {
  console.log('只执行一次');
}, []);

// 3. 有依赖 - 依赖变化时执行
useEffect(() =&gt; {
  console.log('count 变化时执行');
}, [count]);</code></pre>

              <p>记住：<strong>所有在 effect 中使用的组件内的值都应该出现在依赖数组中</strong>。</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="feature-highlight">
      <h3 style="margin-top: 0; color: var(--text-primary);">无边框设计的优势</h3>
      
      <div class="feature-grid">
        <div>
          <h4 style="color: var(--accent-primary); margin-bottom: 0.5rem;">视觉效果</h4>
          <ul class="feature-list">
            <li>AI回复更自然融入背景</li>
            <li>减少视觉干扰和分割感</li>
            <li>内容阅读更加流畅</li>
            <li>整体界面更加简洁</li>
          </ul>
        </div>
        <div>
          <h4 style="color: var(--success); margin-bottom: 0.5rem;">用户体验</h4>
          <ul class="feature-list">
            <li>保持用户消息的边框区分</li>
            <li>长文本阅读体验更佳</li>
            <li>代码块和列表显示更清晰</li>
            <li>符合现代聊天应用趋势</li>
          </ul>
        </div>
      </div>

      <div class="highlight-box">
        <p style="margin: 0; color: var(--text-primary); font-weight: 600;">
          🎯 设计理念：通过差异化的边框处理，让用户消息保持明确的边界感，而AI回复则更自然地融入对话流中。
        </p>
      </div>
    </div>
  </div>
</body>
</html>
