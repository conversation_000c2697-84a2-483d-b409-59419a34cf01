<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Grok风格联网搜索 - 测试页面</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 2rem;
      min-height: 100vh;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
    }

    .title {
      text-align: center;
      font-size: 2rem;
      font-weight: 600;
      margin-bottom: 2rem;
      color: var(--text-primary);
    }

    .demo-section {
      background: var(--bg-secondary);
      border: 1px solid var(--border-primary);
      border-radius: var(--radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
    }

    .section-title {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--text-primary);
    }

    /* 现代化输入框样式 */
    .input-container {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-xl);
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      box-shadow: var(--shadow-sm);
      padding: 0.75rem;
      display: flex;
      align-items: end;
      gap: 0.75rem;
    }

    .input-container:focus-within {
      border-color: var(--accent-primary);
      box-shadow: 0 0 0 3px var(--accent-light);
    }

    .message-input {
      flex: 1;
      background: transparent;
      border: none;
      outline: none;
      color: var(--text-primary);
      font-size: 0.95rem;
      line-height: 1.5;
      resize: none;
      min-height: 48px;
      max-height: 200px;
      padding: 0.5rem;
    }

    .message-input::placeholder {
      color: var(--text-tertiary);
    }

    /* Grok风格的输入框内联网搜索按钮 */
    .input-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .web-search-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 32px;
      height: 32px;
      border-radius: var(--radius-md);
      background: transparent;
      border: 1px solid var(--border-secondary);
      cursor: pointer;
      transition: all 0.2s ease;
      color: var(--text-tertiary);
      position: relative;
      flex-shrink: 0;
    }

    .web-search-btn:hover {
      background: var(--bg-hover);
      border-color: var(--border-primary);
      color: var(--text-secondary);
    }

    .web-search-btn.active {
      background: var(--accent-light);
      border-color: var(--accent-primary);
      color: var(--accent-primary);
    }

    .web-search-btn.active::after {
      content: '';
      position: absolute;
      top: -2px;
      right: -2px;
      width: 8px;
      height: 8px;
      background: var(--accent-primary);
      border-radius: 50%;
      border: 2px solid var(--bg-tertiary);
    }

    .web-search-icon {
      width: 16px;
      height: 16px;
    }

    /* 工具提示 */
    .tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: var(--bg-primary);
      color: var(--text-primary);
      padding: 0.5rem 0.75rem;
      border-radius: var(--radius-md);
      font-size: 0.75rem;
      white-space: nowrap;
      opacity: 0;
      visibility: hidden;
      transition: all 0.2s ease;
      z-index: 1000;
      border: 1px solid var(--border-secondary);
      box-shadow: var(--shadow-lg);
      margin-bottom: 0.5rem;
    }

    .tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: var(--border-secondary);
    }

    .web-search-btn:hover .tooltip {
      opacity: 1;
      visibility: visible;
    }

    .send-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-md);
    }

    .send-btn:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .send-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .status-display {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      padding: 1rem;
      margin-top: 1rem;
    }

    .status-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5rem 0;
      border-bottom: 1px solid var(--border-primary);
    }

    .status-item:last-child {
      border-bottom: none;
    }

    .status-label {
      font-weight: 500;
      color: var(--text-secondary);
    }

    .status-value {
      color: var(--text-primary);
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    }

    .status-value.active {
      color: var(--accent-primary);
    }

    .description {
      color: var(--text-tertiary);
      line-height: 1.6;
      margin-bottom: 1rem;
    }

    .feature-list {
      margin: 1rem 0;
      padding-left: 1.5rem;
      color: var(--text-secondary);
    }

    .feature-list li {
      margin-bottom: 0.5rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">Grok风格联网搜索</h1>
    
    <div class="demo-section">
      <h2 class="section-title">输入框内集成联网搜索</h2>
      <p class="description">
        参考 Grok.com 的设计，将联网搜索按钮集成在输入框内部，提供更紧凑和直观的用户体验。
      </p>
      
      <!-- Grok风格输入框 -->
      <div class="input-container">
        <textarea
          id="messageInput"
          class="message-input"
          placeholder="输入您的问题..."
          rows="1"
        ></textarea>
        <div class="input-actions">
          <!-- 联网搜索按钮 -->
          <button
            type="button"
            id="webSearchBtn"
            class="web-search-btn"
            title="联网搜索"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="web-search-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0 9c-1.657 0-3-4.03-3-9s1.343-9 3-9m0 9c1.657 0 3 4.03 3 9s-1.343 9-3 9m-9 9a9 9 0 019-9" />
            </svg>
            <div class="tooltip" id="webSearchTooltip">联网搜索：已关闭</div>
          </button>
          
          <!-- 发送按钮 -->
          <button
            type="button"
            id="sendButton"
            class="send-btn"
            disabled
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
      </div>

      <div class="status-display">
        <div class="status-item">
          <span class="status-label">联网搜索状态:</span>
          <span class="status-value" id="currentStatus">已关闭</span>
        </div>
        <div class="status-item">
          <span class="status-label">本地存储:</span>
          <span class="status-value" id="localStorageValue">null</span>
        </div>
        <div class="status-item">
          <span class="status-label">输入内容:</span>
          <span class="status-value" id="inputContent">空</span>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h2 class="section-title">设计特点</h2>
      <div class="description">
        <p><strong>Grok风格特点:</strong></p>
        <ul class="feature-list">
          <li>联网搜索按钮集成在输入框内部</li>
          <li>紧凑的布局，节省垂直空间</li>
          <li>激活状态有明显的视觉指示器（蓝色圆点）</li>
          <li>悬停时显示工具提示</li>
          <li>与发送按钮保持一致的设计语言</li>
        </ul>
        
        <p><strong>交互优化:</strong></p>
        <ul class="feature-list">
          <li>一键切换，操作简单直观</li>
          <li>状态持久化，刷新页面保持设置</li>
          <li>实时状态反馈和工具提示</li>
          <li>响应式设计，适配移动端</li>
          <li>无障碍访问支持</li>
        </ul>
      </div>
    </div>
  </div>

  <script>
    // 联网搜索状态
    const webSearchState = {
      enabled: false
    };

    // DOM元素
    const elements = {
      webSearchBtn: document.getElementById('webSearchBtn'),
      webSearchTooltip: document.getElementById('webSearchTooltip'),
      messageInput: document.getElementById('messageInput'),
      sendButton: document.getElementById('sendButton'),
      currentStatus: document.getElementById('currentStatus'),
      localStorageValue: document.getElementById('localStorageValue'),
      inputContent: document.getElementById('inputContent')
    };

    // 初始化
    function init() {
      // 加载保存的状态
      const savedState = localStorage.getItem('webSearchEnabled');
      if (savedState !== null) {
        webSearchState.enabled = JSON.parse(savedState);
      }
      
      updateUI();
      setupEventListeners();
    }

    // 设置事件监听器
    function setupEventListeners() {
      elements.webSearchBtn.addEventListener('click', toggleWebSearch);
      elements.messageInput.addEventListener('input', handleInputChange);
      elements.sendButton.addEventListener('click', handleSend);
    }

    // 切换联网搜索状态
    function toggleWebSearch() {
      webSearchState.enabled = !webSearchState.enabled;
      updateUI();
      saveState();
    }

    // 处理输入变化
    function handleInputChange() {
      const inputValue = elements.messageInput.value.trim();
      elements.sendButton.disabled = !inputValue;
      elements.inputContent.textContent = inputValue || '空';
      
      // 自动调整高度
      elements.messageInput.style.height = 'auto';
      elements.messageInput.style.height = Math.min(elements.messageInput.scrollHeight, 200) + 'px';
    }

    // 处理发送
    function handleSend() {
      const message = elements.messageInput.value.trim();
      if (!message) return;
      
      const messageData = {
        message: message,
        webSearch: webSearchState.enabled,
        timestamp: new Date().toISOString()
      };
      
      console.log('发送消息:', messageData);
      alert(`消息发送成功！\n内容: ${message}\n联网搜索: ${webSearchState.enabled ? '已启用' : '已禁用'}`);
      
      // 清空输入框
      elements.messageInput.value = '';
      handleInputChange();
    }

    // 更新UI
    function updateUI() {
      elements.webSearchBtn.classList.toggle('active', webSearchState.enabled);
      elements.webSearchTooltip.textContent = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
      elements.webSearchBtn.title = `联网搜索：${webSearchState.enabled ? '已开启' : '已关闭'}`;
      
      // 更新状态显示
      elements.currentStatus.textContent = webSearchState.enabled ? '已开启' : '已关闭';
      elements.currentStatus.classList.toggle('active', webSearchState.enabled);
      
      elements.localStorageValue.textContent = localStorage.getItem('webSearchEnabled') || 'null';
    }

    // 保存状态
    function saveState() {
      localStorage.setItem('webSearchEnabled', JSON.stringify(webSearchState.enabled));
      updateUI();
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', init);
  </script>
</body>
</html>
