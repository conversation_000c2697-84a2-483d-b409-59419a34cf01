<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>秒答 - 测试页面</title>
  <style>
    :root {
      --bg-primary: #0a0a0a;
      --bg-secondary: #111111;
      --bg-tertiary: #1a1a1a;
      --bg-hover: #222222;
      --border-primary: #2a2a2a;
      --border-secondary: #333333;
      --text-primary: #ffffff;
      --text-secondary: #b3b3b3;
      --text-tertiary: #888888;
      --accent-primary: #3b82f6;
      --accent-hover: #2563eb;
      --accent-light: rgba(59, 130, 246, 0.1);
      --success: #10b981;
      --warning: #f59e0b;
      --error: #ef4444;
      --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      --radius-sm: 0.375rem;
      --radius-md: 0.5rem;
      --radius-lg: 0.75rem;
      --radius-xl: 1rem;
    }

    * {
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
      background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
      color: var(--text-primary);
      margin: 0;
      padding: 0;
      overflow-x: hidden;
      height: 100vh;
    }

    .container {
      display: flex;
      height: 100vh;
    }

    .sidebar {
      width: 280px;
      background: var(--bg-secondary);
      border-right: 1px solid var(--border-primary);
      padding: 1rem;
      display: flex;
      flex-direction: column;
    }

    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .header {
      padding: 1rem;
      border-bottom: 1px solid var(--border-primary);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .message-area {
      flex: 1;
      padding: 2rem;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .message-bubble {
      max-width: 85%;
      border-radius: var(--radius-xl);
      padding: 1rem 1.25rem;
      line-height: 1.6;
      backdrop-filter: blur(10px);
      border: 1px solid var(--border-primary);
      word-break: break-word;
      overflow-wrap: break-word;
      position: relative;
      transition: all 0.2s ease;
    }

    .user-message {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      align-self: flex-end;
      border-bottom-right-radius: var(--radius-sm);
      box-shadow: var(--shadow-md);
    }

    .assistant-message {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      align-self: flex-start;
      border-bottom-left-radius: var(--radius-sm);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--border-secondary);
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: 600;
      box-shadow: var(--shadow-sm);
      border: 2px solid var(--border-primary);
    }

    .user-avatar {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
    }

    .assistant-avatar {
      background: linear-gradient(135deg, var(--success), #059669);
      color: white;
    }

    .input-area {
      padding: 1rem;
      background: var(--bg-secondary);
      border-top: 1px solid var(--border-primary);
    }

    .tool-selector {
      position: relative;
      margin-bottom: 0.75rem;
    }

    .tool-trigger {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 0.875rem;
      color: var(--text-secondary);
    }

    .tool-trigger:hover {
      border-color: var(--accent-primary);
      background: var(--accent-light);
    }

    .input-container {
      background: var(--bg-tertiary);
      border: 1px solid var(--border-secondary);
      border-radius: var(--radius-xl);
      padding: 0.75rem;
      display: flex;
      align-items: center;
      gap: 0.75rem;
    }

    .message-input {
      flex: 1;
      background: transparent;
      border: none;
      outline: none;
      color: var(--text-primary);
      font-size: 0.95rem;
      resize: none;
    }

    .send-btn {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .send-btn:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .btn-primary {
      background: linear-gradient(135deg, var(--accent-primary), var(--accent-hover));
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--shadow-md);
      display: flex;
      align-items: center;
      gap: 0.5rem;
      width: 100%;
      justify-content: center;
    }

    .btn-primary:hover {
      transform: translateY(-1px);
      box-shadow: var(--shadow-lg);
    }

    .message-header {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
    }

    @media (max-width: 768px) {
      .sidebar {
        display: none;
      }
      
      .message-bubble {
        max-width: 95%;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- 侧边栏 -->
    <div class="sidebar">
      <button class="btn-primary">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        新建对话
      </button>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部 -->
      <div class="header">
        <h1 style="font-size: 1.125rem; font-weight: 600;">秒答</h1>
        <button style="padding: 0.5rem; background: var(--bg-tertiary); border: 1px solid var(--border-secondary); border-radius: var(--radius-md); color: var(--text-secondary);">
          退出登录
        </button>
      </div>

      <!-- 消息区域 -->
      <div class="message-area">
        <!-- 用户消息示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-end;">
          <div class="message-header" style="flex-direction: row-reverse;">
            <div class="avatar user-avatar">👤</div>
            <span style="font-weight: 500; font-size: 0.875rem;">你</span>
          </div>
          <div class="message-bubble user-message">
            你好，请帮我分析一下这个数据库的性能问题。
          </div>
        </div>

        <!-- AI消息示例 -->
        <div style="display: flex; flex-direction: column; align-items: flex-start;">
          <div class="message-header">
            <div class="avatar assistant-avatar">🤖</div>
            <span style="font-weight: 500; font-size: 0.875rem;">秒答</span>
          </div>
          <div class="message-bubble assistant-message">
            我很乐意帮您分析数据库性能问题。为了提供更准确的分析，我需要了解一些具体信息：
            <br><br>
            1. <strong>数据库类型</strong>：MySQL、PostgreSQL、Oracle等？
            <br>
            2. <strong>具体症状</strong>：查询慢、连接超时、CPU使用率高等？
            <br>
            3. <strong>数据量规模</strong>：表的大小、记录数量等
            <br><br>
            您可以提供这些信息，我会为您制定针对性的优化方案。
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <!-- 工具选择器 -->
        <div class="tool-selector">
          <div class="tool-trigger">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 9.172V5L8 4z" />
            </svg>
            <span>选择工具</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="input-container">
          <textarea class="message-input" placeholder="输入您的问题..." rows="1"></textarea>
          <button class="send-btn">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          </button>
        </div>
        
        <div style="text-align: center; margin-top: 0.5rem; font-size: 0.75rem; color: var(--text-tertiary);">
          按 Enter 发送，Shift + Enter 换行
        </div>
      </div>
    </div>
  </div>
</body>
</html>
