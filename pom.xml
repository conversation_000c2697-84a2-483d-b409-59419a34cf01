<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.3</version>
        <relativePath/>
    </parent>

    <groupId>com.yy.hd</groupId>
    <artifactId>hd-ai-agent</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>hd-ai-agent-server</module>
        <module>hd-ai-agent-web</module>
        <module>hd-ai-agent-ranking</module>
        <module>hd-ai-agent-model</module>
        <module>hd-ai-agent-commons</module>
        <module>hd-ai-agent-mcp</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>
        <spring.ai.version>1.1.0-SNAPSHOT</spring.ai.version>
        <spring-s2s-discovery.version>3.4.0-SNAPSHOT</spring-s2s-discovery.version>
        <apollo.version>2.4.0</apollo.version>
        <jsqlparser.version>4.9</jsqlparser.version>
        <guava.version>33.4.8-jre</guava.version>
        <commons-lang3.version>3.17.0</commons-lang3.version>
        <commons-collections.version>3.2.1</commons-collections.version>
        <langchain4j.onnx.version>1.0.0-beta3</langchain4j.onnx.version>
        <baidu-infoflow.version>1.0.0-SNAPSHOT</baidu-infoflow.version>
        <lombok.version>1.18.36</lombok.version>
        <commons-codec.version>1.17.2</commons-codec.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>${apollo.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>

        <dependency>
            <groupId>com.yy</groupId>
            <artifactId>baidu-infoflow-support</artifactId>
            <version>${baidu-infoflow.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yy</groupId>
                <artifactId>baidu-infoflow-support</artifactId>
                <version>${baidu-infoflow.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring.ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.yy.hd</groupId>
                <artifactId>spring-cloud-starter-hd-s2s-discovery</artifactId>
                <version>${spring-s2s-discovery.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>https://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>yyent-release</id>
            <name>yyent-release Repository</name>
            <url>https://nexus.yy.com/music/content/repositories/yyent-release/</url>
        </repository>
        <snapshotRepository>
            <id>yyent</id>
            <name>yyent Repository</name>
            <url>https://nexus.yy.com/music/content/repositories/yyent/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
        <repository>
            <id>public</id>
            <name>YYEnt Public Repositories</name>
            <url>http://nexus.yy.com/music/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
    </repositories>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>repackage</id>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>