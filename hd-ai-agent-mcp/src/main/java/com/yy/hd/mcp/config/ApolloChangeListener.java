package com.yy.hd.mcp.config;

import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.scope.refresh.RefreshScope;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ApolloChangeListener {

    private final RefreshScope refreshScope;

    public ApolloChangeListener(RefreshScope refreshScope) {
        this.refreshScope = refreshScope;
    }

    @ApolloConfigChangeListener(value = {"application.yml"}, interestedKeyPrefixes = { "alicloud.logs" })
    public void refreshMysqlSchema(ConfigChangeEvent changeEvent) {
        log.info("changes for namespace:{}", changeEvent.getNamespace());
        refreshScope.refreshAll();
    }

}
