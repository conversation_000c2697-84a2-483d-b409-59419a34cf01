package com.yy.hd.mcp.service.logs;


import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.config.AliCloudLogSchemaConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AliCloudLogService {

    private final RestTemplate restTemplate;

    private final AliCloudLogSchemaConfig aliCloudLogSchemaConfig;

    public AliCloudLogService(RestTemplate restTemplate, AliCloudLogSchemaConfig aliCloudLogSchemaConfig) {
        this.restTemplate = restTemplate;
        this.aliCloudLogSchemaConfig = aliCloudLogSchemaConfig;
    }

    private static final String QUERY_LOG_URL = HttpUris.ALI_CLOUD_LOG_QUERY_URI;

    private static final int MAX_PAGE = 50;

    private static final int LINE = 100;

    private static final Duration TIMEOUT = Duration.ofSeconds(20);

    public boolean checkServer(String server) {
        return aliCloudLogSchemaConfig.getLogs().containsKey(server);
    }

    public List<String> getServerList() {
        return new ArrayList<>(aliCloudLogSchemaConfig.getLogs().keySet());
    }

    public AliCloudLogParseResult getAliCloudLogs(String server, String query, String fromTime, String toTime) {
        if (StringUtils.isBlank(query)) {
            throw new IllegalArgumentException("请输入云日志搜索关键字");
        }
        AliCloudLogSchemaConfig.AliCloudLogSchema aliCloudLogSchema = aliCloudLogSchemaConfig.getLogs().get(server);
        if (aliCloudLogSchema == null) {
            throw new IllegalArgumentException("找不到云日志对应的服务");
        }
        String project = aliCloudLogSchema.getProject();
        String logStore = aliCloudLogSchema.getLogStore();
        if (StringUtils.isBlank(fromTime)) {
            fromTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        if (StringUtils.isBlank(toTime)) {
            toTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        }
        long fromTimestamp = LocalDateTime.parse(fromTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.ofHours(8));
        long toTimestamp = LocalDateTime.parse(toTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).toEpochSecond(ZoneOffset.ofHours(8));
        int offset = 0;
        int page = 1;
        int total = MAX_PAGE;
        List<String> terms = new ArrayList<>();
        List<AliCloudLog> logs = new ArrayList<>();
        try {
            while (page <= total) {
                URIBuilder builder = new URIBuilder(QUERY_LOG_URL);
                builder.addParameter("project", project);
                builder.addParameter("logStore", logStore);
                builder.addParameter("from", String.valueOf(fromTimestamp));
                builder.addParameter("to", String.valueOf(toTimestamp));
                builder.addParameter("line", String.valueOf(LINE));
                builder.addParameter("offset", String.valueOf(offset));
                builder.addParameter("query", query);
                String url = builder.build().toString();

                String content = restTemplate.getForObject(url, String.class);
                log.info("getAliCloudLogs done, url:{}", url);
                AliCloudLogQueryResponse aliCloudLogQueryResponse = JsonUtils.fromJson(content, AliCloudLogQueryResponse.class);
                if (aliCloudLogQueryResponse.getData() == null || CollectionUtils.isEmpty(aliCloudLogQueryResponse.getData().getLogs())) {
                    log.warn("getAliCloudLogs rsp, server:{}, query:{}, fromTime:{}, toTime:{}, content:{}", server, query, fromTime, toTime, content);
                    break;
                }
                List<String> keywords = aliCloudLogQueryResponse.getData()
                        .getMTerms()
                        .stream()
                        .flatMap(List::stream)
                        .filter(StringUtils::isNotBlank)
                        .toList();
                if (CollectionUtils.isEmpty(terms)) {
                    terms.addAll(keywords);
                }
                for (AliCloudLogQueryResponse.LogItem logItem : aliCloudLogQueryResponse.getData().getLogs()) {
                    Map<String, String> contentsMap = logItem.getMLogItem().getMContents().stream().collect(Collectors.toMap(AliCloudLogQueryResponse.LogContentItem::getMKey, AliCloudLogQueryResponse.LogContentItem::getMValue));
                    AliCloudLog aliCloudLog = new AliCloudLog(
                            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.LEVEL.getColumn()),
                            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.TIME.getColumn()),
                            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.TRACE.getColumn()),
                            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.MESSAGE.getColumn())
                            );
                    logs.add(aliCloudLog);
                }
                offset += LINE;
                page++;
            }
            log.info("queryAliCloudLog logs size:{}", logs.size());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return new AliCloudLogParseResult(logs, terms);
    }
}
