package com.yy.hd.mcp.config;

import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.HttpComponentsClientHttpRequestFactoryBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
public class RestTemplateConfig {

    @Bean
    public RestTemplate restTemplate() {
        HttpComponentsClientHttpRequestFactoryBuilder requestFactoryBuilder = ClientHttpRequestFactoryBuilder.httpComponents()
                .withCustomizer(httpComponentsClientHttpRequestFactory -> {
                    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
                    connectionManager.setMaxTotal(200);
                    connectionManager.setDefaultMaxPerRoute(20);
                    CloseableHttpClient httpClient = HttpClients.custom()
                            .setConnectionManager(connectionManager)
                            .build();
                    httpComponentsClientHttpRequestFactory.setHttpClient(httpClient);
                    httpComponentsClientHttpRequestFactory.setConnectionRequestTimeout(Duration.ofSeconds(5));
                    httpComponentsClientHttpRequestFactory.setConnectTimeout(Duration.ofSeconds(5));
                    httpComponentsClientHttpRequestFactory.setReadTimeout(Duration.ofSeconds(60));
                });
        return new RestTemplateBuilder()
                .requestFactoryBuilder(requestFactoryBuilder)
                .connectTimeout(Duration.ofSeconds(5))
                .readTimeout(Duration.ofSeconds(60))
                .build();
    }

}
