package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLExplainTool {

    private final MySQLReader mysqlReader;

    @Tool(name = "MySQLExplainTool", description = "查询sql语句的执行计划")
    public String mysqlExplainTool(String sql) {
        try {
            Map<String, List<String>> datas = mysqlReader.read(sql);
            return JsonUtils.toJson(datas);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlExplainTool error", e);
        }
        return "查询sql语句的执行计划失败";
    }
}
