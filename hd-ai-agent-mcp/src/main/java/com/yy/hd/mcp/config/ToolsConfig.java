package com.yy.hd.mcp.config;

import org.reflections.Reflections;
import org.reflections.scanners.Scanners;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.execution.DefaultToolExecutionExceptionProcessor;
import org.springframework.ai.tool.execution.ToolExecutionExceptionProcessor;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Configuration
public class ToolsConfig {

    private static final String TOOLS_PACKAGE = "com.yy.hd.mcp.tools";

    @Bean
    public ToolCallbackProvider toolCallbackProvider(ApplicationContext applicationContext) {
        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage(TOOLS_PACKAGE))
                .setScanners(Scanners.MethodsAnnotated));
        Set<Method> methods = reflections.getMethodsAnnotatedWith(Tool.class);
        List<Object> tools = new ArrayList<>();
        for (Method method : methods) {
            Object tool = applicationContext.getBean(method.getDeclaringClass());
            tools.add(tool);
        }
        return MethodToolCallbackProvider
                .builder()
                .toolObjects(tools.toArray())
                .build();
    }

    @Bean
    public ToolExecutionExceptionProcessor toolExecutionExceptionProcessor() {
        return new DefaultToolExecutionExceptionProcessor(true);
    }

}
