package com.yy.hd.mcp.mysql;

import com.google.common.base.Splitter;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.SimpleNode;
import net.sf.jsqlparser.statement.DescribeStatement;
import net.sf.jsqlparser.statement.ExplainStatement;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.UnsupportedStatement;
import net.sf.jsqlparser.statement.UseStatement;
import net.sf.jsqlparser.statement.select.Limit;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.select.SetOperationList;
import net.sf.jsqlparser.statement.show.ShowTablesStatement;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Spliterators;
import java.util.StringJoiner;

public class SqlParser {

    public static Map<String, List<String>> parseSql(String sql) {
        Map<String, List<String>> sqlMap = new HashMap<>();
        try {
            String useDatabase = null;
            List<Statement> statements = CCJSqlParserUtil.parseStatements(sql);
            for (Statement stmt : statements) {
                if (stmt instanceof SetOperationList setOperationList) {
                    List<Select> selects = setOperationList.getSelects();
                    for (Select select : selects) {
                        PlainSelect plainSelect = select.getPlainSelect();
                        SimpleNode simpleNode = plainSelect.getFromItem().getASTNode();
                        if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                            throw new IllegalArgumentException("请指定数据库：" + stmt);
                        }
                        String database = simpleNode.jjtGetFirstToken()
                                .toString()
                                .replace("_", "-");
                        plainSelect = rewriteSql(plainSelect);
                        checkSelect(plainSelect);
                        sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                                .add(plainSelect.toString());
                    }

                } else if (stmt instanceof PlainSelect plainSelect) {
                    SimpleNode simpleNode = plainSelect.getFromItem().getASTNode();
                    if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                        throw new IllegalArgumentException("请指定数据库：" + stmt);
                    }
                    String database = simpleNode.jjtGetFirstToken()
                            .toString()
                            .replace("_", "-");
                    plainSelect = rewriteSql(plainSelect);
                    checkSelect(plainSelect);
                    sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                            .add(plainSelect.toString());
                } else if (stmt instanceof UseStatement useStatement) {
                    if (useDatabase != null) {
                        throw new IllegalArgumentException("不支持多个use语句：" + sql);
                    }
                    useDatabase = useStatement.getName()
                            .replace("_", "-");
                } else if (stmt instanceof ShowTablesStatement showTablesStatement) {
                    sqlMap.computeIfAbsent(useDatabase, k -> new ArrayList<>())
                            .add(showTablesStatement.toString());
                } else if (stmt instanceof DescribeStatement descStatement) {
                    SimpleNode simpleNode = descStatement.getTable().getASTNode();
                    if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                        throw new IllegalArgumentException("请指定数据库：" + stmt);
                    }
                    String database = simpleNode.jjtGetFirstToken()
                            .toString()
                            .replace("_", "-");
                    sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                            .add(descStatement.toString());
                } else if (stmt instanceof ExplainStatement explainStatement) {
                    PlainSelect plainSelect = explainStatement.getStatement().getPlainSelect();
                    SimpleNode simpleNode = plainSelect.getFromItem().getASTNode();
                    if (StringUtils.equalsIgnoreCase(simpleNode.jjtGetFirstToken().toString(), simpleNode.jjtGetLastToken().toString())) {
                        throw new IllegalArgumentException("请指定数据库：" + stmt);
                    }
                    String database = simpleNode.jjtGetFirstToken()
                            .toString()
                            .replace("_", "-");
                    sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                            .add(explainStatement.toString());
                } else if (stmt instanceof UnsupportedStatement unsupportedStatement) {
                    String unsupportedSql = unsupportedStatement.toString();
                    if (unsupportedSql.toUpperCase().startsWith("SHOW CREATE TABLE")) {
                        List<String> declarations = Splitter.on(" ").splitToList(unsupportedSql);
                        String table = declarations.getLast();
                        String[] databaseAndTable = table.split("\\.");
                        if (databaseAndTable.length != 2) {
                            throw new IllegalArgumentException("请指定数据库：" + stmt);
                        }
                        String database = databaseAndTable[0].replace("_", "-");
                        sqlMap.computeIfAbsent(database, k -> new ArrayList<>())
                                .add(unsupportedSql);
                    }
                }
                else {
                    throw new IllegalArgumentException("不支持的SQL语句：" + stmt);
                }
            }
            if (MapUtils.isEmpty(sqlMap)) {
                throw new IllegalArgumentException("不支持的SQL语句：" + sql);
            }
        } catch (JSQLParserException e) {
            throw new IllegalArgumentException("SQL语法错误：" + sql);
        }
        return sqlMap;
    }

    private static PlainSelect rewriteSql(PlainSelect plainSelect) {
        int maxLimit = 50;
        Limit limit = plainSelect.getLimit();
        if (limit == null) {
            limit = new Limit();
            limit.withRowCount(new LongValue(maxLimit));
            plainSelect.setLimit(limit);
        } else {
            if (limit.getRowCount(LongValue.class).getValue() > maxLimit) {
                limit.withRowCount(new LongValue(maxLimit));
            }
        }
        return plainSelect;
    }

    private static void checkSelect(PlainSelect plainSelect) {
        if (plainSelect.getForMode() != null || plainSelect.getForUpdateTable() != null) {
            throw new IllegalArgumentException("不支持FOR UPDATE：" + plainSelect);
        }
        Limit limit = plainSelect.getLimit();
        if (limit == null) {
            throw new IllegalArgumentException("SQL语句必须包含limit：" + plainSelect);
        }
    }

    public static void main(String[] args) {
        String sql = "SHOW  CREATE TABLE zy_user_risk_log;";
        System.out.println(parseSql(sql));
    }
}
