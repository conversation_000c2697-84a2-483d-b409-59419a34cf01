package com.yy.hd.mcp.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Slf4j
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "alicloud")
public class AliCloudLogSchemaConfig {

    private Map<String, AliCloudLogSchema> logs;

    public Map<String, AliCloudLogSchema> getLogs() {
        return logs;
    }

    public void setLogs(Map<String, AliCloudLogSchema> logs) {
        this.logs = logs;
    }

    @Data
    public static class AliCloudLogSchema {
        private String project;
        private String logStore;
    }

}
