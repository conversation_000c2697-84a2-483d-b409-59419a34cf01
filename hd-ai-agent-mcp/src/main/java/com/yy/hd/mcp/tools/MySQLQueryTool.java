package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLQueryTool {

    private final MySQLReader mysqlReader;

    @Tool(name = "MySQLQueryTool", description = "执行sql查询语句：select ...")
    public String mysqlQueryTool(String sql) {
        try {
            Map<String, List<String>> datas = mysqlReader.read(sql);
            return JsonUtils.toJson(datas);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlQueryTool error", e);
        }
        return "查询mysql数据失败";
    }
}
