package com.yy.hd.mcp.service.logs;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class AliCloudLogQueryResponse {

    private String msg;
    private int code;
    private LogQueryData data;
    private boolean success;

    /**
     * 日志查询数据
     */
    @Data
    public static class LogQueryData {
        private boolean mIsCompleted;
        private String mMarker;
        private String mAggQuery;
        private String mWhereQuery;
        private boolean mHasSQL;
        private int mProcessedRow;
        private int mElapsedMilliSecond;
        private int mLimited;
        private double mCpuSec;
        private int mCpuCores;
        private boolean mIsPhraseQuery;
        private boolean mScanAll;
        private int mBeginOffset;
        private int mEndOffset;
        private int mEndTime;
        private int mShard;
        private int mScanBytes;
        private int mQueryMode;
        private List<String> mKeys;
        private List<List<String>> mTerms;
        private List<LogItem> logs;
        private Map<String, String> mHeaders;
    }

    /**
     * 日志项
     */
    @Data
    public static class LogItem {
        private String mSource;
        private LogContent mLogItem;
    }

    /**
     * 日志内容
     */
    @Data
    public static class LogContent {
        private long mLogTime;
        private List<LogContentItem> mContents;
    }

    /**
     * 日志内容项
     */
    @Data
    public static class LogContentItem {
        private String mKey;
        private String mValue;
    }

    public enum MKeyColumn {
        LEVEL("level"),
        TIME("time"),
        MESSAGE("message"),
        TRACE("trace");

        private String column;

        MKeyColumn(String column) {
            this.column = column;
        }
        public String getColumn() {
            return column;
        }

        public static MKeyColumn fromKey(String column) {
            for (MKeyColumn mKeyColumn : MKeyColumn.values()) {
                if (mKeyColumn.column.equalsIgnoreCase(column)) {
                    return mKeyColumn;
                }
            }
            return null;
        }
    }
}
