package com.yy.hd.mcp.service.bos;

import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.yy.hd.commons.uri.HttpUris;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Slf4j
@AllArgsConstructor
@Service
public class BosService  {

    private final RestTemplate restTemplate;

    private static final String BOS_URL = HttpUris.BOS_UPLOAD_TOKEN_URI;

    private GetUploadTokenResp getTokenFromBos(String fileName) {
        try {
            GetUploadTokenReq req = new GetUploadTokenReq();
            req.setBusiId("hd-mcp-server");
            req.setUserId(0L);
            req.setFileName(fileName);
            req.setScene(1);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<GetUploadTokenReq> httpEntity = new HttpEntity<>(req, httpHeaders);
            ResponseEntity<BosResponse> response = restTemplate.exchange(BOS_URL, HttpMethod.POST,
                    httpEntity, BosResponse.class);
            BosResponse bosResponse = response.getBody();
            log.info("getTokenFromBos bosResponse:{}", bosResponse);
            if (bosResponse.getCode() == 0) {
                return bosResponse.getData();
            }
        } catch (Exception e) {
            log.error("getTokenFromBos error", e);
        }
        return null;
    }

    public String uploadMinFileData(byte[] binaryData, String fileName, String bucketName) {
        GetUploadTokenResp tokenResp = getTokenFromBos(fileName);
        if (tokenResp == null) {
            return null;
        }
        BosClient bosClient = buildTemporaryBosClient(tokenResp.getEndPoint(), tokenResp.getAk(), tokenResp.getSk(), tokenResp.getToken());
        bosClient.putObject(bucketName, tokenResp.getFileName(), binaryData);
        bosClient.shutdown();
        return tokenResp.getBs2Url() + tokenResp.getFileName();
    }

    private BosClient buildTemporaryBosClient(String endPoint, String ak, String sk, String token) {
        return new BosClient(new BosClientConfiguration()
                .withEndpoint(endPoint)
                .withCredentials(new DefaultBceSessionCredentials(ak, sk, token)));
    }

}
