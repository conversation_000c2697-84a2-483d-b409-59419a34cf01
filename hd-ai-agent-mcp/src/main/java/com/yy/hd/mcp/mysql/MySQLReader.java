package com.yy.hd.mcp.mysql;

import com.alibaba.cloud.ai.reader.mysql.MySQLDocumentReader;
import com.alibaba.cloud.ai.reader.mysql.MySQLResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class MySQLReader {

    private final Binder binder;

    public MySQLReader(Environment environment) {
        this.binder = Binder.get(environment);
    }

    public List<String> databaseList() {
        Map<String, Object> rawMap = binder.bindOrCreate("spring.datasource", Map.class);
        Map<String, DatasourceProperties> result = new HashMap<>();
        for (Map.Entry<String, Object> entry : rawMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value instanceof DatasourceProperties) {
                result.put(key, (DatasourceProperties) value);
            } else {
                try {
                    DatasourceProperties datasourceProperties = binder.bindOrCreate("spring.datasource." + key, DatasourceProperties.class);
                    result.put(key, datasourceProperties);
                } catch (Exception e) {
                    log.error("bind datasourceProperties error", e);
                }
            }
        }
        return result
                .values()
                .stream()
                .map(DatasourceProperties::getDatabase)
                .sorted()
                .toList();
    }

    public Map<String, List<String>> read(String query) {
        Map<String, List<String>> result = new HashMap<>();
        Map<String, List<String>> sqlMap = SqlParser.parseSql(query);
        for (Map.Entry<String, List<String>> entry : sqlMap.entrySet()) {
            String database = entry.getKey();
            List<String> sqlList = entry.getValue();
            DatasourceProperties datasourceProperties = binder.bindOrCreate("spring.datasource." + database, DatasourceProperties.class);
            for (String sql : sqlList) {
                MySQLResource mySQLResource = new MySQLResource(datasourceProperties.getHost(), datasourceProperties.getPort(),
                        datasourceProperties.getDatabase(), datasourceProperties.getUsername(),
                        datasourceProperties.getPassword(), sql, null, null);
                List<String> datas = new MySQLDocumentReader(mySQLResource).read()
                                .stream()
                                        .map(Document::getText)
                                        .toList();
                result.put(sql, datas);
            }
        }
        return result;
    }

}
