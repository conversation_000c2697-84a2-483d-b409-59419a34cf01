package com.yy.hd.mcp.service.bos;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BosAcl {

    /**
     * 请求权限
     */
    public static class Permission {
        private Permission() {
            throw new UnsupportedOperationException();
        }

        public static final String READ = "READ";
        public static final String WRITE = "WRITE";
        public static final String LIST = "LIST";
        public static final String GET_OBJECT = "GetObject";

        /**
         * 该权限表示允许用户进行上传Object的相关操作，例如PutObject、PostObject、AppendObject、FetchObject、CopyObject、三步上传、三步Copy
         **/
        public static final String PUT_OBJECT = "PutObject";

    }

    /**
     * 指定与该条acl配置项匹配的Request能否执行，取值为Allow或Deny。Allow表示可以执行；Deny表示拒绝执行。
     */
    private String effect = "Allow";

    /**
     * acl配置项影响的区域，"*"表示所有区域。
     */
    private String region = "*";

    /**
     * 請求服務
     */
    private String service = "bce:bos";

    /**
     * acl配置项所影响的资源，支持通配符。如：<BucketName>/<ObjectKey>或<BucketName>/xxx*
     */
    private List<String> resource;

    /**
     * 请求权限, 參考 {@link Permission}
     */
    private List<String> permission = Arrays.asList(Permission.READ, Permission.WRITE, Permission.LIST, Permission.GET_OBJECT);

    public BosAcl() {
    }

    public BosAcl(List<String> resource) {
        this.resource = resource;
    }
}
