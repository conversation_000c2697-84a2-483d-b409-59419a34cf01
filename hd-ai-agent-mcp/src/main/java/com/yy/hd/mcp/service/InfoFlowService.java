package com.yy.hd.mcp.service;

import com.alibaba.fastjson.JSON;
import com.yy.bis.robot.BisRobotMessage;
import com.yy.bis.robot.BisRobotService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> huangmin
 * @data : 2021/10/12
 */
@Slf4j
@AllArgsConstructor
@Service
public class InfoFlowService {

    private final BisRobotService bisRobotService;

    public void syncSend(Integer baiduGroupId, String webhook, String msg) {
        List<String> subList = splitString(msg, 2048);
        for (String subMsg : subList) {
            send(baiduGroupId, webhook, subMsg);
        }
    }

    private void send(Integer baiduGroupId, String webhook, String msg) {
        if (StringUtils.isBlank(msg)) {
            return;
        }
        BisRobotMessage message = BisRobotMessage.builder()
                .appendMarkdown(msg)
                .toids(Collections.singleton(baiduGroupId.longValue()));
        var ret = bisRobotService.syncSend(webhook, message);
        log.info("syncSend groupId:{}, msg:{}, response:{}", baiduGroupId, msg, JSON.toJSONString(ret));
    }

    private static List<String> splitString(String input, int length) {
        List<String> result = new ArrayList<>();
        for (int i = 0; i < input.length(); i += length) {
            int end = Math.min(i + length, input.length());
            result.add(input.substring(i, end));
        }
        return result;
    }

}
