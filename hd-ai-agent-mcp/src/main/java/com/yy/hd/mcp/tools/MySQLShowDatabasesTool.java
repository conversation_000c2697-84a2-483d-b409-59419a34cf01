package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLShowDatabasesTool {

    private final MySQLReader mysqlReader;

    @Tool(name = "MySQLShowDatabasesTool", description = "查询mysql数据库列表")
    public String mysqlShowDatabasesTool() {
        try {
            List<String> datas = mysqlReader.databaseList();
            return JsonUtils.toJson(datas);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlShowDatabasesTool error", e);
        }
        return "查询数据库列表失败";
    }
}
