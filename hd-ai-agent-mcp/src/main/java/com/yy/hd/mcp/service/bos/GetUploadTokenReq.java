package com.yy.hd.mcp.service.bos;


import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 获取bos上传Token请求
 *
 * <AUTHOR>
 * @since 2023/4/3 11:31
 **/
@Data
public class GetUploadTokenReq {

    /**
     * 业务id
     **/
    private String busiId;
    /**
     * 文件名字
     **/
    private String fileName;
    /**
     * 文件名列表
     **/
    private List<String> fileNames;
    /**
     * 场景id
     **/
    private Integer scene;
    /**
     * 登录的userId(从登录态获取,本地测试时可自己填写)
     **/
    private Long userId;

    private long yyNo;

    /**
     * 兼容app旧参数
     **/
    private Integer tokenType;

    private String ticket;

    private String source;

    /** 扩展信息 */
    private String extJson;

    /** 直接指定使用的bucket */
    private String bucket;

    /** 设定有效期 */
    private Integer expireSeconds;

    /** 要申请的权限, 默认是上传 */
    private List<String> permissions = Lists.newArrayList(BosAcl.Permission.PUT_OBJECT);

    /** 待上传的文件数量 */
    private int fileCount;

    /** 是否添加 env 环境前缀（生产环境始终不添加），0-默认使用系统配置，1-添加前缀 2-不添加 */
    private int appendEnvPrefix;

    /** 上传来源referer */
    private String referer;

    /** referer 说明 */
    private String refererDesc;

    public boolean valid() {
        if (StringUtils.isEmpty(busiId)) {
            return false;
        }

        if (StringUtils.isEmpty(fileName) && CollectionUtils.isEmpty(fileNames)) {
            return false;
        }

        boolean needSetScene = tokenType != null && tokenType == 1 && (scene == null || scene < 0);
        if (needSetScene) {
            scene = 1;
        }

        if (scene == null || scene < 0) {
            return false;
        }

        return true;
    }
}
