package com.yy.hd.mcp.service.bos;

import lombok.Data;

import java.util.List;

/**
 * 获取bos上传Token响应
 *
 * <AUTHOR>
 * @since 2023/4/3 11:37
 **/
@Data
public class GetUploadTokenResp {
    /**
     * 临时的accessKey
     **/
    private String ak;
    /**
     * 临时的secretKey
     **/
    private String sk;
    /**
     * 临时token
     **/
    private String token;
    /**
     * token过期时间,毫秒级时间戳
     **/
    private long expiration;
    /**
     * bucket区域
     **/
    private String endPoint;
    /**
     * bucket名称
     **/
    private String bucket;
    /**
     * bs2域名,加上fileName之后是完整的地址
     **/
    private String bs2Url;
    /**
     * 入参透传
     **/
    private String busiId;
    /**
     * 文件路径
     **/
    private String pathPrefix;
    /**
     * 带有pathPrefix的文件完整名
     **/
    private String fileName;

    /** 文件列表 */
    private List<String> fileNames;

    /** cdn域名，如 zhuiya.bs2cdn.yy.com 客户端自己构造 https://${cdnDomain}/${fileName} 即为完整的资源地址 */
    private String cdnDomain;

    /** token有效期，单位：秒 */
    private Integer expirationSeconds;

    public GetUploadTokenResp() {
    }

}
