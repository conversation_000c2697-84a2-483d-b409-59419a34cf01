package com.yy.hd.mcp.tools;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.mysql.MySQLReader;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@AllArgsConstructor
@Component
public class MySQLShowCreateTableTool {

    private final MySQLReader mysqlReader;

    private static final String DESC_SQL = "SHOW CREATE TABLE %s.%s;";

    @Tool(name = "MySQLShowCreateTableTool", description = "mysql数据表结构：show create table like ...")
    public String mysqlShowCreateTableTool(String database, String table) {
        String sql = String.format(DESC_SQL, database, table);
        try {
            Map<String, List<String>> datas = mysqlReader.read(sql);
            return JsonUtils.toJson(datas);
        } catch (IllegalArgumentException e) {
            return e.getMessage();
        } catch (Exception e) {
            log.error("mysqlShowCreateTableTool error", e);
        }
        return "查询mysql数据表结构失败";
    }
}
