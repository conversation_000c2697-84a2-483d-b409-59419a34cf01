package com.yy.hd.server.config;

import com.yy.hd.model.advisor.RateLimiteAdvisor;
import com.yy.hd.server.chat.advisor.LoggerAdvisor;
import com.yy.hd.server.chat.memory.AiChatMemoryRepository;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class AdvisorsConfig {

    @Bean
    public RateLimiteAdvisor rateLimiteAdvisor() {
        return new RateLimiteAdvisor();
    }

    @Bean
    public LoggerAdvisor loggerAdvisor() {
        return new LoggerAdvisor();
    }

    @Bean
    public PromptChatMemoryAdvisor promptChatMemoryAdvisor(AiChatMemoryRepository chatMemoryRepository) {
        ChatMemory chatMemory = MessageWindowChatMemory.builder()
                .maxMessages(10)
                .chatMemoryRepository(chatMemoryRepository)
                .build();
        return PromptChatMemoryAdvisor.builder(chatMemory)
                .build();
    }

    @Bean
    public List<Advisor> defaultAdvisors(RateLimiteAdvisor rateLimiteAdvisor, LoggerAdvisor loggerAdvisor,
                                  PromptChatMemoryAdvisor promptChatMemoryAdvisor) {
        return List.of(rateLimiteAdvisor, loggerAdvisor, promptChatMemoryAdvisor);
    }
}
