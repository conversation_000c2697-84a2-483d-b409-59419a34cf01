package com.yy.hd.server.entity;

import lombok.Data;

/**
 * <AUTHOR> 2020/8/28
 */
@Data
public class Response<T> {

    /**
     * 0-成功, 其他失败
     */
    private int result;

    /**
     * 通常为错误提示
     */
    private String msg = "OK";

    /**
     * 携带的数据，为简单类型 或 对象类型
     */
    private T data;

    public static <T> Response<T> success() {
        return new Response<>();
    }

    public static <T> Response<T> success(T data) {
        Response<T> response = new Response<>();
        response.setData(data);
        return response;
    }

    public static <T> Response<T> fail(int result, String msg) {
        Response<T> response = new Response<>();
        response.setResult(result);
        response.setMsg(msg);
        return response;
    }

    public static <T> Response<T> fail(int result, String msg, T data) {
        Response<T> response = new Response<>();
        response.setResult(result);
        response.setMsg(msg);
        response.setData(data);
        return response;
    }

}
