package com.yy.hd.server.service;

import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.server.dto.UpdateDocumentsDTO;
import com.yy.hd.server.dto.BM25SearchDTO;
import com.yy.hd.server.dto.DocumentDTO;
import com.yy.hd.server.dto.FineRankingDTO;
import com.yy.hd.server.dto.SimilaritySearchDTO;
import org.springframework.ai.document.Document;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;

import java.util.List;

@Service
public class RankingService {

    private final WebClient loadBalancedWebClient;

    private final RestTemplate loadBalancedRestTemplate;

    public RankingService(WebClient.Builder loadBalancedWebClientBuilder, RestTemplateBuilder loadBalancedRestTemplateBuilder) {
        this.loadBalancedWebClient = loadBalancedWebClientBuilder.build();
        this.loadBalancedRestTemplate = loadBalancedRestTemplateBuilder.build();
    }

    public void documents(String searchKey, List<Document> documents) {
        UpdateDocumentsDTO body = new UpdateDocumentsDTO(searchKey, convert2DTO(documents));
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<UpdateDocumentsDTO> httpEntity = new HttpEntity<>(body, httpHeaders);
        loadBalancedRestTemplate.postForObject(HttpUris.RANKING_DOCUMENTS_URI, httpEntity, String.class);
    }

    public Flux<DocumentDTO> bm25Search(String searchKey, String query, int topN) {
        return loadBalancedWebClient.post()
                .uri(HttpUris.RANKING_BM25_SEARCH_URI)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new BM25SearchDTO(searchKey, query, topN))
                .retrieve()
                .bodyToFlux(DocumentDTO.class);
    }

    public Flux<DocumentDTO> similaritySearch(String searchKey, String query, double similarity, int topK, String filterExpression) {
        return loadBalancedWebClient.post()
                .uri(HttpUris.RANKING_SIMILARITY_SEARCH_URI)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new SimilaritySearchDTO(searchKey, query, similarity, topK, filterExpression))
                .retrieve()
                .bodyToFlux(DocumentDTO.class);
    }

    public Flux<DocumentDTO> fineRanking(String query, List<DocumentDTO> documents, double score, int limit) {
        return loadBalancedWebClient.post()
                .uri(HttpUris.RANKING_FINE_RANKING_URI)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(new FineRankingDTO(query, documents, score, limit))
                .retrieve()
                .bodyToFlux(DocumentDTO.class);
    }

    private List<DocumentDTO> convert2DTO(List<Document> documents) {
        return documents.stream()
                .map(document -> new DocumentDTO(document.getId(), document.getText(), document.getMetadata(), document.getScore()))
                .toList();
    }

}
