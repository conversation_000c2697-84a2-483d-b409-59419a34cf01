package com.yy.hd.server.graph;

import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

public enum SourceType {

    UNKNOWN("unknown"),
    INFOFLOW("infoflow"),
    WEB("web");

    private final String source;

    SourceType(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public static SourceType fromType(String source) {
        SourceType sourceType = null;
        try {
            if (StringUtils.isNotBlank(source)) {
                sourceType = SourceType.valueOf(source.toUpperCase());
            }
        } catch (IllegalArgumentException e) {
            sourceType = SourceType.UNKNOWN;
        }
        return Optional.ofNullable(sourceType).orElse(SourceType.UNKNOWN);
    }
}
