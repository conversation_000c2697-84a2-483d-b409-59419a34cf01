package com.yy.hd.server.filter;

import com.duowan.udb.auth.UserinfoForOauth;
import com.duowan.universal.login.client.YYSecCenterOpenWSInvoker;
import com.yy.hd.commons.AgentHttpHeaderKeys;
import com.yy.hd.commons.utils.MD5Utils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class LoginFilter implements WebFilter {

    @Value("${udb.appid}")
    private String udbAppId;

    @Value("${udb.appkey}")
    private String udbAppKey;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        return Mono.just(exchange)
                .publishOn(Schedulers.boundedElastic())
                .flatMap(ex -> {
                    if (checkRefererSign(exchange.getRequest().getHeaders())) {
                        return chain.filter(exchange);
                    }
                    HttpServletRequest request = new HttpServletRequestAdapter(exchange);
                    UserinfoForOauth oauth = new UserinfoForOauth(request, null, udbAppId, udbAppKey);
                    boolean validFlag = oauth.validate();
                    if (validFlag) {
                        String yyuid = oauth.getYyuid();
                        String accessToken = oauth.getAccesstoken();
                        String tokenSecret = oauth.getTokensecret();
                        //显式请求UDB服务端进行accessToken的验证，业务可根据场景是否验证accesstoken。  accesstoken在用户改密、开启登录保护等操作后会失效
                        boolean tokenValidResult = YYSecCenterOpenWSInvoker.validAccessTokenByYyuid(udbAppId, udbAppKey, accessToken, tokenSecret, yyuid);
                        if (tokenValidResult) {
                            long uid = Long.parseLong(yyuid);
                            if (uid > 0L) {
                                return chain.filter(exchange);
                            }
                        }
                    }
                    ServerHttpResponse httpResponse = exchange.getResponse();
                    httpResponse.setStatusCode(HttpStatusCode.valueOf(HttpStatus.UNAUTHORIZED.value()));
                    String data = "{\"code\":" + HttpStatus.UNAUTHORIZED.value() + ",\"msg\":\"" + HttpStatus.UNAUTHORIZED.getReasonPhrase() + "\"}";
                    DataBuffer buffer = httpResponse.bufferFactory().wrap(data.getBytes(StandardCharsets.UTF_8));
                    return httpResponse.writeWith(Mono.just(buffer));
                });
    }

    private boolean checkRefererSign(HttpHeaders headers) {
        String source = headers.getFirst(AgentHttpHeaderKeys.SOURCE_HEADER);
        String referer = headers.getFirst(HttpHeaders.REFERER);
        String headerSign = headers.getFirst(AgentHttpHeaderKeys.REFERER_SIGN_HEADER);
        log.info("checkRefererSign source:{}, referer:{}, headerSign:{}", source, referer, headerSign);
        if (StringUtils.isNotBlank(source) && StringUtils.isNotBlank(referer) && StringUtils.isNotBlank(headerSign)) {
            String sign = MD5Utils.getMD5(source + referer);
            String originSign = headerSign.substring(0, headerSign.length() - 6);
            log.info("checkRefererSign sign:{}, originSign:{}", sign, originSign);
            return StringUtils.equalsIgnoreCase(sign, originSign);
        }
        return false;
    }
}
