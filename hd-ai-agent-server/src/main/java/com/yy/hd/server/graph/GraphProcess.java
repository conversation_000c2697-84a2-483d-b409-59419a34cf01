/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.yy.hd.server.graph;

import com.alibaba.cloud.ai.graph.NodeOutput;
import com.alibaba.cloud.ai.graph.async.AsyncGenerator;
import com.alibaba.cloud.ai.graph.streaming.StreamingOutput;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Sinks;

import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @since 2025/6/6 15:05
 */
@Slf4j
public class GraphProcess {

	private final ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());

	public void processStream(AsyncGenerator<NodeOutput> generator, Sinks.Many<ServerSentEvent<String>> sink) {
		executor.submit(() -> {
			generator.forEachAsync(output -> {
				try {
					// logger.info("output = {}", output);
					String nodeName = output.node();
					String content = "";
					if (output instanceof StreamingOutput streamingOutput) {
						content = streamingOutput.chunk();
						//content = JsonUtils.toJson(Map.of(nodeName, streamingOutput.chunk()));
						//logger.info("Streaming output from node {}: {}", nodeName, streamingOutput.chunk());
					}
					else {
						JSONObject nodeOutput = new JSONObject();
						nodeOutput.put("data", output.state().data());
						nodeOutput.put("node", nodeName);
						content = JSON.toJSONString(nodeOutput);
					}
					sink.tryEmitNext(ServerSentEvent.builder(content).build());
				}
				catch (Exception e) {
					log.error("Error processing output", e);
					throw new CompletionException(e);
				}
			}).thenAccept(v -> {
				// 正常完成
				sink.tryEmitComplete();
			}).exceptionally(e -> {
				log.error("Error in stream processing", e);
				sink.tryEmitError(e);
				return null;
			});
		});
	}

	public void processOne(AssistantMessage assistantMessage, Sinks.One<ServerSentEvent<String>> sink) {
		try {
			String content = assistantMessage.getText();
			sink.tryEmitValue(ServerSentEvent.builder(content).build());
		}
		catch (Exception e) {
			log.error("Error processing output", e);
			throw new RuntimeException(e);
		}
	}

}
