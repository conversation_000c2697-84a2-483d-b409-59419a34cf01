package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.yy.hd.server.dto.DocumentDTO;
import com.yy.hd.server.graph.BM25SearchContext;
import com.yy.hd.server.graph.SimilaritySearchContext;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.graph.RankingContext;
import com.yy.hd.server.service.RankingService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 工具召回
 */
@AllArgsConstructor
@Component
public class ToolRetrieverNode implements NodeAction {

    @Resource
    private RankingService rankingService;

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey()).orElse(null);
        String query = workflowContext.getInput();
        String searchRankingKey = workflowContext.getRankingKey();

        // 1、bm25召回
        BM25SearchContext bm25SearchContext = workflowContext.getBm25SearchContext();
        Flux<DocumentDTO> bm25Documents = rankingService.bm25Search(searchRankingKey, query, bm25SearchContext.topN());

        // 2、向量召回
        SimilaritySearchContext similaritySearchContext = workflowContext.getSimilaritySearchContext();
        Flux<DocumentDTO> embeddingDocuments = rankingService.similaritySearch(searchRankingKey, query,
                similaritySearchContext.similarity(), similaritySearchContext.topK(), similaritySearchContext.filterExpression());

        // 3、合并bm25和向量召回的结果，并去重
        Flux<DocumentDTO> searchDocuments = Flux.merge(bm25Documents, embeddingDocuments)
                .distinct(DocumentDTO::id);

        // 4、根据bm25和向量召回的结果，进行reranking
        RankingContext rankingContext = workflowContext.getRankingContext();
        List<String> toolNames =  searchDocuments.collectList()
                .flatMapMany(documents -> rankingService.fineRanking(query, documents,
                        rankingContext.score(), rankingContext.limit()))
                .map(DocumentDTO::id)
                .collectList()
                .block();
        workflowContext.setToolNames(toolNames);
        return Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
    }

}
