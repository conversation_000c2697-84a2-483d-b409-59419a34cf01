package com.yy.hd.server.config;

import com.yy.hd.model.ChatClientProvider;
import com.yy.hd.model.ChatClientProviderBuilder;
import com.yy.hd.model.advisor.RateLimiteAdvisor;
import com.yy.hd.model.properties.ModelRoutingContext;
import com.yy.hd.server.chat.advisor.LoggerAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class AiConfig {

    @Bean
    @ConfigurationProperties(prefix = "spring.ai")
    public ModelRoutingContext modelContext() {
        return new ModelRoutingContext();
    }

    @Bean
    public ChatClientProvider chatClientSelector(ModelRoutingContext modelRoutingContext,
                                                 RateLimiteAdvisor rateLimiteAdvisor,
                                                 LoggerAdvisor loggerAdvisor) {
        List<Advisor> advisors = List.of(loggerAdvisor, rateLimiteAdvisor);
        return ChatClientProviderBuilder.builder()
            .context(modelRoutingContext)
            .advisors(advisors)
            .build();
    }

}
