package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.yy.hd.server.graph.SourceType;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.alibaba.cloud.ai.graph.StateGraph.END;

/**
 * 召回/重排上下文设置
 */
@Component
public class SourceAnalysisNode implements NodeAction {

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey())
                .orElse(null);
        SourceType sourceType = SourceType.fromType(workflowContext.getSource());
        if (sourceType == SourceType.UNKNOWN) {
            workflowContext.setNextNodeId(END);
            return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), "参数校验失败！");
        }
        workflowContext.setNextNodeId("rankingContextNode");
        return Map.of(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext);
    }

}
