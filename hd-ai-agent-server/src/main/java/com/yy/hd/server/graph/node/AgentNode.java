package com.yy.hd.server.graph.node;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.OverAllState;
import com.alibaba.cloud.ai.graph.action.NodeAction;
import com.alibaba.cloud.ai.graph.node.ToolNode;
import com.alibaba.cloud.ai.graph.state.strategy.AppendStrategy;
import com.yy.hd.model.ChatClientSelector;
import com.yy.hd.server.graph.SourceType;
import com.yy.hd.server.graph.WorkflowContext;
import com.yy.hd.server.graph.WorkflowRegisterKeys;
import com.yy.hd.server.graph.agent.ReactAgent;
import com.yy.hd.server.service.McpToolService;
import com.yy.hd.server.service.PromptService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 大模型节点
 */
@Component
public class AgentNode implements NodeAction {

    @Resource
    private PromptService promptService;

    @Resource
    private PromptChatMemoryAdvisor promptChatMemoryAdvisor;

    @Resource
    private ChatClientSelector chatClientSelector;

    @Resource
    private McpToolService mcpToolService;

    @Resource
    private LLMNode llmNode;

    @Override
    public Map<String, Object> apply(OverAllState state) throws Exception {
        WorkflowContext workflowContext = (WorkflowContext) state.value(WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey()).orElse(null);
        String uid = workflowContext.getUid();
        String chatId = workflowContext.getChatId();
        String source = workflowContext.getSource();
        String query = workflowContext.getInput();
        List<String> toolNames = workflowContext.getToolNames();
        List<AssistantMessage> outputMessages = new ArrayList<>();
        List<ToolCallback> toolCallbacks = mcpToolService.getTools(toolNames);
        if (CollectionUtils.isEmpty(toolCallbacks)) {
            toolCallbacks = switch (SourceType.fromType(source)) {
                case WEB -> mcpToolService.getTools();
                default -> Collections.emptyList();
            };
        }
        if (CollectionUtils.isNotEmpty(toolCallbacks)) {
            ToolNode toolNode = ToolNode.builder()
                    .toolCallbacks(toolCallbacks)
                    .build();

            ReactAgent agent = new ReactAgent(llmNode, toolNode, 5,
                    () -> {
                        HashMap<String, KeyStrategy> keyStrategyHashMap = new HashMap<>();
                        keyStrategyHashMap.put("messages", new AppendStrategy());
                        return keyStrategyHashMap;
                    }, null, null);
            CompiledGraph graph = agent.getAndCompileGraph();

            outputMessages = graph.invoke(Map.of("messages", List.of(new UserMessage(query)),
                            WorkflowRegisterKeys.WORKFLOW_CONTEXT_KEY.getKey(), workflowContext))
                    .map(overAllState -> {
                        List<Message> messages = (List<Message>) overAllState.value("messages").orElseThrow();
                        AssistantMessage message = (AssistantMessage) messages.get(messages.size() - 1);
                        return message;
                    })
                    .stream()
                    .toList();

        } else {
            outputMessages.add(new AssistantMessage("没有工具"));
        }
        return Map.of(WorkflowRegisterKeys.MESSAGES_KEY.getKey(), outputMessages);
    }
}
