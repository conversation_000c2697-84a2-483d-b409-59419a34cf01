/*
 * Copyright 2024-2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.alibaba.cloud.ai.graph;

/**
 * Factory interface for creating instances of {@link OverAllState}.
 *
 * <p>
 * {@link OverAllStateFactory} provides a functional interface for generating new
 * instances of the overall state used in graph-based workflows. This can be useful for
 * initializing states with specific configurations, such as setting default values or
 * applying custom strategies.
 *
 * <h2>Usage Example</h2> <pre>{@code
 * OverAllStateFactory factory = () -> new OverAllState();
 * OverAllState state = factory.create();
 * }</pre>
 *
 * <AUTHOR>
 * @since 1.0.0.1
 */
@Deprecated
@FunctionalInterface
public interface OverAllStateFactory {

	/**
	 * Creates a new instance of {@link OverAllState}.
	 * @return a new instance of the overall state
	 */
	OverAllState create();

}
