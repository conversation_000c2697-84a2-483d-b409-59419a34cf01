# AI大模型配置文件
# 支持多提供商、基础/高级模型、多API Key配置

ai:
  models:
    # 默认提供商
    default-provider: openai
    
    # 用户模型偏好设置
    user-preferences:
      # 默认使用的模型层级：basic（基础）或 advanced（高级）
      default-tier: basic
      # 是否允许用户切换模型层级
      allow-tier-switching: true
      # 模型切换策略：manual（手动）或 auto（自动）
      switching-strategy: manual
    
    # 大模型提供商配置
    providers:
      # OpenAI 配置
      openai:
        # 提供商名称
        name: "OpenAI"
        # 提供商描述
        description: "OpenAI GPT系列模型"
        # API基础URL
        base-url: "https://api.openai.com"
        # 是否启用
        enabled: true
        # 多个API Key配置，支持负载均衡
        api-keys:
          - "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
          - "sk-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy"
          - "sk-zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
        # 模型列表
        models:
          # 基础模型
          basic:
            - model-name: "gpt-3.5-turbo"
              display-name: "GPT-3.5 Turbo"
              description: "快速响应的基础对话模型"
              max-tokens: 4096
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.002
            - model-name: "gpt-3.5-turbo-16k"
              display-name: "GPT-3.5 Turbo 16K"
              description: "支持长文本的基础模型"
              max-tokens: 16384
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.004
          # 高级模型
          advanced:
            - model-name: "gpt-4"
              display-name: "GPT-4"
              description: "最强大的推理和创作模型"
              max-tokens: 8192
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.03
            - model-name: "gpt-4-turbo"
              display-name: "GPT-4 Turbo"
              description: "更快的GPT-4模型"
              max-tokens: 128000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.01
            - model-name: "gpt-4o"
              display-name: "GPT-4o"
              description: "多模态GPT-4模型"
              max-tokens: 128000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.005

      # Mistral AI 配置
      mistral:
        name: "Mistral AI"
        description: "Mistral系列开源模型"
        base-url: "https://api.mistral.ai"
        enabled: true
        api-keys:
          - "hONnJQCS1KM1V563lDijo0JP5PbBYekj"
          - "YwlJYBzT0kJ8NGPJzp0bpiTSoVuB7Ljn"
          - "r5VHZjFjyolC9i8NBp7TLxaAit6DEAzt"
          - "1EdQGenxuj9wzsQrWAMyFmTGeZKpJjw4"
        models:
          basic:
            - model-name: "mistral-small-latest"
              display-name: "Mistral Small"
              description: "轻量级快速模型"
              max-tokens: 32768
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.001
            - model-name: "mistral-medium-latest"
              display-name: "Mistral Medium"
              description: "平衡性能的中等模型"
              max-tokens: 32768
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.0025
          advanced:
            - model-name: "mistral-large-latest"
              display-name: "Mistral Large"
              description: "最强大的Mistral模型"
              max-tokens: 32768
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.008

      # Claude 配置
      claude:
        name: "Anthropic Claude"
        description: "Claude系列对话模型"
        base-url: "https://api.anthropic.com"
        enabled: true
        api-keys:
          - "sk-ant-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
          - "sk-ant-yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy"
        models:
          basic:
            - model-name: "claude-3-haiku-20240307"
              display-name: "Claude 3 Haiku"
              description: "快速响应的基础模型"
              max-tokens: 200000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.00025
          advanced:
            - model-name: "claude-3-sonnet-20240229"
              display-name: "Claude 3 Sonnet"
              description: "平衡性能的高级模型"
              max-tokens: 200000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.003
            - model-name: "claude-3-opus-20240229"
              display-name: "Claude 3 Opus"
              description: "最强大的Claude模型"
              max-tokens: 200000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.015

      # 智谱AI 配置
      zhipu:
        name: "智谱AI"
        description: "智谱GLM系列模型"
        base-url: "https://open.bigmodel.cn/api/paas/v4"
        enabled: true
        api-keys:
          - "xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.xxxxxxxxxxxxxx"
          - "yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy.yyyyyyyyyyyyyy"
        models:
          basic:
            - model-name: "glm-3-turbo"
              display-name: "GLM-3 Turbo"
              description: "快速的基础对话模型"
              max-tokens: 128000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.0005
          advanced:
            - model-name: "glm-4"
              display-name: "GLM-4"
              description: "智谱最新一代大模型"
              max-tokens: 128000
              temperature: 0.7
              enabled: true
              cost-per-1k-tokens: 0.1

    # 路由配置
    routing:
      # 路由策略：round-robin（轮询）、weighted-round-robin（加权轮询）、random（随机）
      strategy: round-robin
      # 重试配置
      retry:
        max-attempts: 3
        backoff:
          initial-interval: 1000
          multiplier: 2.0
          max-interval: 10000
      # 超时配置（毫秒）
      timeout:
        connect-timeout: 30000
        read-timeout: 60000
      # 限流配置
      rate-limit:
        # 每分钟最大请求数
        requests-per-minute: 100
        # 每小时最大请求数
        requests-per-hour: 1000

    # 监控配置
    monitoring:
      # 是否启用监控
      enabled: true
      # 监控指标
      metrics:
        - "request_count"
        - "response_time"
        - "error_rate"
        - "token_usage"
        - "cost_tracking"
      # 告警配置
      alerts:
        # 错误率阈值（百分比）
        error-rate-threshold: 5.0
        # 响应时间阈值（毫秒）
        response-time-threshold: 5000
        # 成本阈值（美元/小时）
        cost-threshold: 10.0
