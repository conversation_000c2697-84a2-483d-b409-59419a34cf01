// 方法1：直接创建JsonSchema对象
Map<String, Object> properties = new HashMap<>();
properties.put("query", Map.of(
    "type", "string",
    "description", "搜索关键字"
));
properties.put("limit", Map.of(
    "type", "integer",
    "description", "查询条数"
));

JsonSchema schema = new JsonSchema(
    "object",           // type
    properties,         // properties
    List.of("query"),   // required fields
    false,              // additionalProperties
    null,               // defs
    null                // definitions
);

// 方法2：从JSON字符串解析
String schemaJson = """
    {
      "type": "object",
      "properties": {
        "query": {
          "type": "string",
          "description": "搜索关键字"
        },
        "limit": {
          "type": "integer",
          "description": "查询条数"
        }
      },
      "required": ["query"]
    }
    """;
JsonSchema schema = parseSchema(schemaJson);