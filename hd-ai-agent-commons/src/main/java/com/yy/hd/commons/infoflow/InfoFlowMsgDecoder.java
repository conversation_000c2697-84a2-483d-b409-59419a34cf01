package com.yy.hd.commons.infoflow;

import com.yy.hd.commons.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public final class InfoFlowMsgDecoder {

    public static String checkSignatureAndGetEchostr(String queryString, String signToken) {
        Map<String, String> queryParams = parseQueryString(queryString);
        String signature = queryParams.get("signature");
        String timestamp = queryParams.get("timestamp");
        String rn = queryParams.get("rn");
        String str = DigestUtils.md5Hex((rn + timestamp + signToken).getBytes(StandardCharsets.UTF_8));
        if (signature.equals(str)) {
            return queryParams.get("echostr");
        }
        return StringUtils.EMPTY;
    }

    private static Map<String, String> parseQueryString(String queryString) {
        Map<String, String> queryParams = new HashMap<>();
        String[] pairs = queryString.split("&");
        for (String pair : pairs) {
            String[] keyValue = pair.split("=", 2);
            if (keyValue.length == 2) {
                queryParams.put(keyValue[0].trim(), keyValue[1].trim());
            }
        }
        return queryParams;
    }

    public static InfoFlowReceiveMsg decrypt(InputStream inputStream, String encodingAESKey) throws Exception {
        byte[] msgBase64 = Base64.getUrlDecoder().decode(IOUtils.toString(inputStream, StandardCharsets.UTF_8));
        SecretKeySpec skeySpec = new SecretKeySpec(Base64.getDecoder().decode(encodingAESKey), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] decrypted = cipher.doFinal(msgBase64);
        // 通过AES解密后得到回调消息数据
        String decryptedMsg = new String(decrypted, StandardCharsets.UTF_8);
        log.info("decrypt infoflow msg:{}", decryptedMsg);
        return JsonUtils.fromJson(decryptedMsg, InfoFlowReceiveMsg.class);
    }

    public static InfoFlowReceiveMsg decrypt(String msg, String encodingAESKey) throws Exception {
        byte[] msgBase64 = Base64.getUrlDecoder().decode(msg);
        SecretKeySpec skeySpec = new SecretKeySpec(Base64.getDecoder().decode(encodingAESKey), "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] decrypted = cipher.doFinal(msgBase64);
        // 通过AES解密后得到回调消息数据
        String decryptedMsg = new String(decrypted, StandardCharsets.UTF_8);
        log.info("decrypt infoflow msg:{}", decryptedMsg);
        return JsonUtils.fromJson(decryptedMsg, InfoFlowReceiveMsg.class);
    }

    public static CommandAndText getCommandAndText(InfoFlowReceiveMsg infoFlowReceiveMsg) {
        log.info("getCommandAndText receive infoflow msg:{}", JsonUtils.toJson(infoFlowReceiveMsg));
        StringBuilder text = new StringBuilder();
        infoFlowReceiveMsg.getMessage().getBody()
                .stream()
                .filter(msgBody -> "replyData".equalsIgnoreCase(msgBody.getType())
                        || "TEXT".equalsIgnoreCase(msgBody.getType()))
                .map(InfoFlowReceiveMsg.MessageBody::getContent)
                .filter(StringUtils::isNotBlank)
                .forEach(body -> text.append(body).append(";"));
        String command = infoFlowReceiveMsg.getMessage().getBody()
                .stream()
                .filter(msgBody -> "command".equalsIgnoreCase(msgBody.getType()))
                .map(InfoFlowReceiveMsg.MessageBody::getCommandname)
                .findFirst()
                .orElse(null);
        return new CommandAndText(command, text.toString());
    }

    public record CommandAndText(String command, String text) {}
}
