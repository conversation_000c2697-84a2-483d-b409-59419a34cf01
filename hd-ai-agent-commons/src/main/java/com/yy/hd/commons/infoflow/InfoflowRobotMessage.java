package com.yy.hd.commons.infoflow;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> huangmin
 * @data : 2021/10/11
 */
@Data
public class InfoflowRobotMessage {

    private InfoflowRobotMessageHeader header;

    private List<Object> body;

    public InfoflowRobotMessage(final InfoflowRobotMessageHeader header, List<Object> body) {
        this.header = header;
        this.body =  body;
    }

}
