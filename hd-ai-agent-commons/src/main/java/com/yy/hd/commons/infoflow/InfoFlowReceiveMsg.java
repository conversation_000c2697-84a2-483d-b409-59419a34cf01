package com.yy.hd.commons.infoflow;

import lombok.Data;

import java.util.List;

@Data
public class InfoFlowReceiveMsg {
    /** 事件类型，固定值：MESSAGE_RECEIVE */
    private String eventtype;
    private Integer agentid;
    /** 群聊ID */
    private Integer groupid;
    private String corpid;
    private Message message;
    private Long time;
    private Long fromid;
    private String opencode;

    @Data
    public static class Message {
        private Header header;
        private List<MessageBody> body;
    }

    @Data
    public static class Header {
        /** 消息发送者userid（员工的成员ID） */
        private String fromuserid;
        /** 群聊ID */
        private Integer toid;
        /** 消息接收方类型，固定值：GROUP */
        private String totype;
        /** 消息类型，固定值：MIXED */
        private String msgtype;
        private Long clientmsgid;
        private Long messageid;
        private String msgseqid;
        private At at;
        private String compatible;
        private String offlinenotify;
        private String extra;
        private Long servertime;
        private Long clienttime;
        private Long updatetime;
    }

    @Data
    public static class At {
        private List<String> atrobotids;
    }

    @Data
    public static class MessageBody {
        /** replyData-引用历史消息 @机器人，LINK-链接元素，TEXT-文本元素，AT-@元素，IMAGE-图片，command-"/"快捷命令 */
        private String type;
        private Long robotid;
        private String name;
        /** 当type=command时，commandname是快捷命令的名称 */
        private String commandname;
        /** type 为TEXT时必填，文本内容 */
        private String content;
    }
}