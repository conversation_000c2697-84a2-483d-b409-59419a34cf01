package com.yy.hd.model.config;

import com.yy.hd.model.ChatClientProvider;
import com.yy.hd.model.ChatClientProviderBuilder;
import com.yy.hd.model.advisor.LoggerAdvisor;
import com.yy.hd.model.advisor.RateLimiteAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
public class ChatClientProviderConfig {

    @Bean
    public ChatClientProvider chatClientSelector(AiModelConfig aiModelConfig,
                                                 RateLimiteAdvisor rateLimiteAdvisor,
                                                 LoggerAdvisor loggerAdvisor) {
        List<Advisor> advisors = List.of(loggerAdvisor, rateLimiteAdvisor);
        return ChatClientProviderBuilder.builder()
                .context(modelRoutingContext)
                .advisors(advisors)
                .build();
    }
}
