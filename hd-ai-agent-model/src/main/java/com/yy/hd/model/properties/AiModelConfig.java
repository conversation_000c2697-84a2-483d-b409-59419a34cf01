package com.yy.hd.model.config;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * AI模型配置主类
 * 对应ai-models-config.yml的根结构
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiModelConfig {

    /**
     * 默认提供商
     */
    private String defaultProvider;

    /**
     * 大模型提供商配置映射
     * key: 提供商标识（如 openai, mistral）
     * value: 提供商配置
     */
    private Map<String, ModelProviderConfig> providers;



}
