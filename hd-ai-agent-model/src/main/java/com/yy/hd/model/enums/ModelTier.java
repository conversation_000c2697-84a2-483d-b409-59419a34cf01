package com.yy.hd.model.enums;

public enum ModelTier {

    /**
     * 基础模型
     */
    BASIC("basic"),
    /**
     * 高级模型
     */
    ADVANCED("advanced");

    private final String tier;

    ModelTier(String tier) {
        this.tier = tier;
    }

    public String getTier() {
        return tier;
    }

    public static ModelTier fromTier(String tier) {
        try {
            return ModelTier.valueOf(tier.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid model tier: " + tier);
        }
    }
}
