package com.yy.hd.model;

import com.google.common.collect.Lists;
import com.yy.hd.model.config.AiModelConfig;
import com.yy.hd.model.properties.ModelProperties;
import com.yy.hd.model.properties.ModelProviderProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public abstract class AbstractChatClientProvider implements ChatClientProvider {

    private final AiModelConfig aiModelConfig;

    private final ChatClientPool chatClientPool;

    protected AbstractChatClientProvider(AiModelConfig aiModelConfig, ChatClientPool chatClientPool) {
        this.aiModelConfig = aiModelConfig;
        this.chatClientPool = chatClientPool;
    }

    @Override
    public List<ModelProvider> getModelProviders() {
        List<String> providers = chatClientPool.getProviders();
        if (providers != null) {
            return providers.stream()
                    .map(provider -> {
                        List<ModelProvider> modelProviders = new ArrayList<>();
                        Map<String, ModelProviderProperties> modelProviderPropertiesMap = aiModelConfig.getProviders();
                        for (Map.Entry<String, ModelProviderProperties> entry : modelProviderPropertiesMap.entrySet()) {
                            String providerName = entry.getKey();
                            ModelProviderProperties modelProviderProperties = entry.getValue();
                            List<ModelProperties> models = List.of(modelProviderProperties.getBasicModels());
                            modelProviderProperties.getBasicModels();
                            modelProviderProperties.getAdvancedModels();
                        }
                        new ModelProvider(provider, aiModelConfig.getProviders().get(provider));
                    })
                    .toList();
        }
        return List.of();
    }

    protected String getDefaultProvider() {
        return aiModelConfig.getDefaultProvider();
    }

    protected List<String> getApiKeys(String provider) {
        return chatClientPool.getApiKeys(provider);
    }

    /**
     * 获取指定提供商的所有chat client
     * key: 模型层级
     * value: 该层级下的chat client列表
     * @param provider
     * @return
     */
    protected Map<String, List<ChatClientWrapper>> getChatClients(String provider) {
        return chatClientPool.getPool(provider);
    }
}
