package com.yy.hd.model;

import com.yy.hd.model.config.AiModelConfig;
import com.yy.hd.model.properties.ModelProviderProperties;

import java.util.List;
import java.util.Map;

public abstract class AbstractChatClientProvider implements ChatClientProvider {

    private final AiModelConfig aiModelConfig;

    private final ChatClientPool chatClientPool;

    protected AbstractChatClientProvider(AiModelConfig aiModelConfig, ChatClientPool chatClientPool) {
        this.aiModelConfig = aiModelConfig;
        this.chatClientPool = chatClientPool;
    }

    @Override
    public Map<String, ModelProviderProperties> getModelProviders() {
        return aiModelConfig.getProviders();
    }

    protected AiModelConfig.DefaultProvider getDefaultProvider() {
        return aiModelConfig.getDefaultProvider();
    }

    protected List<String> getApiKeys(String provider) {
        return chatClientPool.getApiKeys(provider);
    }

    /**
     * 获取指定提供商的所有chat client
     * key: 模型标识
     * value: 该模型下的chat client列表
     * @param provider
     * @return
     */
    protected Map<String, List<ChatClientWrapper>> getChatClients(String provider) {
        return chatClientPool.getPool(provider);
    }
}
