package com.yy.hd.model;

import java.util.List;

public abstract class Abstract<PERSON>hatClientProvider implements ChatClientProvider {

    private final ChatClientPool chatClientPool;

    protected AbstractChatClientProvider(ChatClientPool chatClientPool) {
        this.chatClientPool = chatClientPool;
    }



    protected List<ChatClientHolder> getChatClients(String provider) {
        return chatClientPool.getChatClients(provider);
    }
}
