package com.yy.hd.model.properties;

import com.yy.hd.model.config.AiModelConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AI模型配置属性类
 * 用于绑定ai-models-config.yml配置文件
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Component
@ConfigurationProperties(prefix = "ai.models")
public class AiModelProperties {

    /**
     * AI模型配置
     */
    private AiModelConfig models;

    /**
     * 获取默认提供商
     * 
     * @return 默认提供商标识符
     */
    public String getDefaultProvider() {
        return models != null ? models.getDefaultProvider() : null;
    }

    /**
     * 设置默认提供商
     * 
     * @param defaultProvider 默认提供商标识符
     */
    public void setDefaultProvider(String defaultProvider) {
        if (models == null) {
            models = new AiModelConfig();
        }
        models.setDefaultProvider(defaultProvider);
    }

    /**
     * 获取用户偏好设置
     * 
     * @return 用户偏好设置
     */
    public AiModelConfig.UserModelPreference getUserPreferences() {
        return models != null ? models.getUserPreferences() : null;
    }

    /**
     * 获取路由配置
     * 
     * @return 路由配置
     */
    public AiModelConfig.RoutingConfig getRouting() {
        return models != null ? models.getRouting() : null;
    }

    /**
     * 获取监控配置
     * 
     * @return 监控配置
     */
    public AiModelConfig.MonitoringConfig getMonitoring() {
        return models != null ? models.getMonitoring() : null;
    }

    /**
     * 检查配置是否有效
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return models != null 
            && models.getProviders() != null 
            && !models.getProviders().isEmpty()
            && models.getDefaultProvider() != null
            && models.getProviders().containsKey(models.getDefaultProvider());
    }

    /**
     * 获取启用的提供商数量
     * 
     * @return 启用的提供商数量
     */
    public long getEnabledProviderCount() {
        if (models == null || models.getProviders() == null) {
            return 0;
        }
        return models.getProviders().values().stream()
            .filter(provider -> Boolean.TRUE.equals(provider.getEnabled()))
            .count();
    }

    /**
     * 检查指定提供商是否存在且启用
     * 
     * @param providerName 提供商名称
     * @return 是否存在且启用
     */
    public boolean isProviderEnabled(String providerName) {
        if (models == null || models.getProviders() == null || providerName == null) {
            return false;
        }
        var provider = models.getProviders().get(providerName);
        return provider != null && Boolean.TRUE.equals(provider.getEnabled());
    }

    /**
     * 获取用户默认模型层级
     * 
     * @return 默认模型层级
     */
    public String getDefaultTier() {
        var preferences = getUserPreferences();
        return preferences != null ? preferences.getDefaultTier() : "basic";
    }

    /**
     * 检查是否允许用户切换模型层级
     * 
     * @return 是否允许切换
     */
    public boolean isAllowTierSwitching() {
        var preferences = getUserPreferences();
        return preferences != null && Boolean.TRUE.equals(preferences.getAllowTierSwitching());
    }

    /**
     * 获取模型切换策略
     * 
     * @return 切换策略
     */
    public String getSwitchingStrategy() {
        var preferences = getUserPreferences();
        return preferences != null ? preferences.getSwitchingStrategy() : "manual";
    }

    /**
     * 获取路由策略
     * 
     * @return 路由策略
     */
    public String getRoutingStrategy() {
        var routing = getRouting();
        return routing != null ? routing.getStrategy() : "round-robin";
    }

    /**
     * 检查是否启用监控
     * 
     * @return 是否启用监控
     */
    public boolean isMonitoringEnabled() {
        var monitoring = getMonitoring();
        return monitoring != null && Boolean.TRUE.equals(monitoring.getEnabled());
    }
}
