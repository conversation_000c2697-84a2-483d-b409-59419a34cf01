package com.yy.hd.model.enums;

import lombok.Getter;

/**
 * 模型层级枚举
 * 定义基础模型和高级模型的分类
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Getter
public enum ModelTierEnum {

    /**
     * 基础模型
     * 特点：响应快速、成本较低、适合日常对话和简单任务
     */
    BASIC("basic", "基础模型", "响应快速、成本较低、适合日常对话和简单任务", 1),

    /**
     * 高级模型
     * 特点：推理能力强、功能丰富、适合复杂任务和专业场景
     */
    ADVANCED("advanced", "高级模型", "推理能力强、功能丰富、适合复杂任务和专业场景", 2);

    /**
     * 层级标识符
     */
    private final String tier;

    /**
     * 层级显示名称
     */
    private final String displayName;

    /**
     * 层级描述
     */
    private final String description;

    /**
     * 层级优先级（数值越小优先级越高）
     */
    private final Integer priority;

    ModelTierEnum(String tier, String displayName, String description, Integer priority) {
        this.tier = tier;
        this.displayName = displayName;
        this.description = description;
        this.priority = priority;
    }

    /**
     * 根据层级标识符获取枚举值
     * 
     * @param tier 层级标识符
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ModelTierEnum fromTier(String tier) {
        if (tier == null || tier.trim().isEmpty()) {
            return null;
        }
        
        for (ModelTierEnum tierEnum : values()) {
            if (tierEnum.tier.equalsIgnoreCase(tier.trim())) {
                return tierEnum;
            }
        }
        return null;
    }

    /**
     * 根据层级标识符获取枚举值，如果不存在则返回默认值
     * 
     * @param tier 层级标识符
     * @param defaultTier 默认值
     * @return 对应的枚举值或默认值
     */
    public static ModelTierEnum fromTierOrDefault(String tier, ModelTierEnum defaultTier) {
        ModelTierEnum result = fromTier(tier);
        return result != null ? result : defaultTier;
    }

    /**
     * 检查给定的层级标识符是否有效
     * 
     * @param tier 层级标识符
     * @return 是否有效
     */
    public static boolean isValidTier(String tier) {
        return fromTier(tier) != null;
    }

    /**
     * 获取所有层级标识符
     * 
     * @return 层级标识符数组
     */
    public static String[] getAllTiers() {
        ModelTierEnum[] values = values();
        String[] tiers = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            tiers[i] = values[i].tier;
        }
        return tiers;
    }

    /**
     * 获取所有层级显示名称
     * 
     * @return 层级显示名称数组
     */
    public static String[] getAllDisplayNames() {
        ModelTierEnum[] values = values();
        String[] displayNames = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            displayNames[i] = values[i].displayName;
        }
        return displayNames;
    }

    /**
     * 检查是否为基础模型层级
     * 
     * @return 是否为基础模型
     */
    public boolean isBasic() {
        return this == BASIC;
    }

    /**
     * 检查是否为高级模型层级
     * 
     * @return 是否为高级模型
     */
    public boolean isAdvanced() {
        return this == ADVANCED;
    }

    /**
     * 获取下一个层级（基础->高级，高级->基础）
     * 
     * @return 下一个层级
     */
    public ModelTierEnum getNextTier() {
        return this == BASIC ? ADVANCED : BASIC;
    }

    /**
     * 比较两个层级的优先级
     * 
     * @param other 另一个层级
     * @return 比较结果（负数表示当前层级优先级更高）
     */
    public int comparePriority(ModelTierEnum other) {
        if (other == null) {
            return -1;
        }
        return this.priority.compareTo(other.priority);
    }

    @Override
    public String toString() {
        return String.format("ModelTierEnum{tier='%s', displayName='%s', description='%s', priority=%d}", 
            tier, displayName, description, priority);
    }
}
