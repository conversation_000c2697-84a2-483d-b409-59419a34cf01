package com.yy.hd.model.config;

import com.yy.hd.model.properties.ModelProviderProperties;
import lombok.Getter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * AI模型配置主类
 * 对应ai-models-config.yml的根结构
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Getter
@Configuration
@ConfigurationProperties(prefix = "ai.models")
public class AiModelConfig {

    /**
     * 默认提供商
     */
    private String defaultProvider;

    /**
     * 大模型提供商配置映射
     * key: 提供商标识（如 openai, mistral）
     * value: 提供商配置
     */
    private Map<String, ModelProviderProperties> providers;

}
