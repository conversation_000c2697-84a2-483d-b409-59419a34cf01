package com.yy.hd.model;

import com.yy.hd.model.config.AiModelConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;
import java.util.Map;
import java.util.NoSuchElementException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

public class ApiKeyRoundRobinProvider extends AbstractChatClientProvider implements ChatClientProvider {

    /**
     * 提供商轮询索引
     * key: 提供商标识
     * value: 轮询索引
     */
    private static final Map<String, AtomicInteger> providerRoundRobinMap = new ConcurrentHashMap<>();

    public ApiKeyRoundRobinProvider(AiModelConfig aiModelConfig, ChatClientPool chatClientPool) {
        super(aiModelConfig, chatClientPool);
        chatClientPool.getProviders()
                .forEach(provider -> providerRoundRobinMap.put(provider, new AtomicInteger(0)));
    }

    @Override
    public ChatClient getDefaultChatClient() {
        return getChatClient(null, null);
    }

    @Override
    public ChatClient getChatClient(String provider, String model) {
        AiModelConfig.DefaultProvider defaultProvider = getDefaultProvider();

        String llmProvider = (StringUtils.isNotBlank(provider)) ? provider : defaultProvider.getProvider();
        String llmModel = (StringUtils.isNotBlank(model)) ? model : defaultProvider.getModel();

        if (StringUtils.isBlank(provider)) {
            provider = defaultProvider.getProvider();
        }
        if (StringUtils.isBlank(model)) {
            model = defaultProvider.getModel();
        }
        String apiKey = roundRobinApiKey(provider);
        Map<String, List<ChatClientWrapper>> tieredChatClients = getChatClients(provider);
        List<ChatClientWrapper> chatClients = tieredChatClients.get(model);
        return chatClients.stream()
                .filter(chatClient -> StringUtils.equalsIgnoreCase(apiKey, chatClient.getApiKey()))
                .findFirst()
                .orElseThrow(new Supplier<Throwable>() {
                    @Override
                    public Throwable get() {
                        return new NoSuchElementException("No chat client found for provider " + provider + " and model: " + model);
                    }
                })
                .getChatClient();
    }

    private String roundRobinApiKey(String provider) {
        AtomicInteger roundRobinIndex = providerRoundRobinMap.get(provider);
        List<String> apiKeys = getApiKeys(provider);
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % apiKeys.size());
        return apiKeys.get(pos);
    }

}
