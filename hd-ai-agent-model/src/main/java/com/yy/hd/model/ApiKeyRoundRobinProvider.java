package com.yy.hd.model;

import com.yy.hd.model.enums.ModelTierEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class ApiKeyRoundRobinProvider extends AbstractChatClientProvider implements ChatClientProvider {

    /**
     * 提供商轮询索引
     * key: 提供商标识
     * value: 轮询索引
     */
    private static final Map<String, AtomicInteger> providerRoundRobinMap = new ConcurrentHashMap<>();

    public ApiKeyRoundRobinProvider(ChatClientPool chatClientPool) {
        super(chatClientPool);
        chatClientPool.getProviders()
                .forEach(provider -> providerRoundRobinMap.put(provider, new AtomicInteger(0)));
    }

    @Override
    public ChatClient getChatClient() {
        String provider = getDefaultProvider();
        String apiKey = roundRobinApiKey(provider);
        Map<String, List<ChatClientWrapper>> tieredChatClients = getChatClients(provider);
        // 默认调用基础模型
        List<ChatClientWrapper> chatClients = tieredChatClients.get(ModelTierEnum.BASIC.getTier());
        return chatClients.stream()
                .filter(chatClient -> StringUtils.equalsIgnoreCase(apiKey, chatClient.getApiKey()))
                .findFirst()
                .get()
                .getChatClient();
    }

    @Override
    public ChatClient getChatClient(String provider, String tier) {
        String apiKey = roundRobinApiKey(provider);
        Map<String, List<ChatClientWrapper>> tieredChatClients = getChatClients(provider);
        List<ChatClientWrapper> chatClients = tieredChatClients.get(tier);
        return chatClients.stream()
                .filter(chatClient -> StringUtils.equalsIgnoreCase(apiKey, chatClient.getApiKey()))
                .findFirst()
                .get()
                .getChatClient();
    }

    private String roundRobinApiKey(String provider) {
        AtomicInteger roundRobinIndex = providerRoundRobinMap.get(provider);
        List<String> apiKeys = getApiKeys(provider);
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % apiKeys.size());
        return apiKeys.get(pos);
    }

}
