package com.yy.hd.model;

import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class RoundRobinProvider extends AbstractChatClientProvider implements ChatClientProvider {

    private static final Map<String, AtomicInteger> providerRoundRobinMap = new ConcurrentHashMap<>();

    public RoundRobinProvider(ChatClientPool chatClientPool) {
        super(chatClientPool);
        chatClientPool.getPool()
                .forEach((provider, v) -> providerRoundRobinMap.put(provider, new AtomicInteger(0)));
    }

    @Override
    public ChatClient getChatClient() {
        String provider = getDefaultProvider();
        List<ChatClientHolder> chatClients = getChatClients(provider);
        AtomicInteger roundRobinIndex = providerRoundRobinMap.get(provider);
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % chatClients.size());
        return chatClients.get(pos)
                .getChatClient();
    }

    @Override
    public ChatClient getChatClient(String provider) {
        List<ChatClientHolder> chatClients = getChatClients(provider);
        AtomicInteger roundRobinIndex = providerRoundRobinMap.get(provider);
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % chatClients.size());
        return chatClients.get(pos)
                .getChatClient();
    }

    @Override
    public ChatClient getChatClient(String provider, String model) {
        AtomicInteger roundRobinIndex = providerRoundRobinMap.get(provider);
        List<ChatClientHolder> chatClients = getChatClients(provider).stream()
                .filter(chatClient -> StringUtils.equalsIgnoreCase(model, chatClient.getModel()))
                .toList();
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % chatClients.size());
        return chatClients.get(pos)
                .getChatClient();
    }

}
