package com.yy.hd.model.config;

import com.yy.hd.model.enums.ModelTierEnum;
import com.yy.hd.model.enums.ProviderTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.List;

/**
 * 用户模型偏好设置类
 * 用于存储和管理用户的模型使用偏好
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserModelPreference {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户首选的提供商
     */
    private String preferredProvider;

    /**
     * 用户首选的模型层级
     */
    private String preferredTier;

    /**
     * 用户首选的具体模型
     */
    private String preferredModel;

    /**
     * 是否启用自动模型选择
     * true: 系统根据任务类型自动选择最适合的模型
     * false: 使用用户指定的模型
     */
    @Builder.Default
    private Boolean autoModelSelection = false;

    /**
     * 成本敏感度设置
     * LOW: 优先选择性能最好的模型，不考虑成本
     * MEDIUM: 在性能和成本之间平衡
     * HIGH: 优先选择成本最低的模型
     */
    @Builder.Default
    private CostSensitivity costSensitivity = CostSensitivity.MEDIUM;

    /**
     * 响应速度偏好
     * FAST: 优先选择响应速度快的模型
     * BALANCED: 平衡响应速度和质量
     * QUALITY: 优先选择输出质量高的模型
     */
    @Builder.Default
    private SpeedPreference speedPreference = SpeedPreference.BALANCED;

    /**
     * 任务类型与模型的映射配置
     * key: 任务类型（如 "chat", "code", "translation", "analysis"）
     * value: 该任务类型首选的模型配置
     */
    private Map<String, TaskModelPreference> taskModelMappings;

    /**
     * 禁用的提供商列表
     */
    private List<String> disabledProviders;

    /**
     * 禁用的模型列表
     */
    private List<String> disabledModels;

    /**
     * 模型使用历史统计
     */
    private ModelUsageStats usageStats;

    /**
     * 偏好设置创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 偏好设置最后更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 成本敏感度枚举
     */
    public enum CostSensitivity {
        LOW("低", "优先性能，不考虑成本"),
        MEDIUM("中", "平衡性能和成本"),
        HIGH("高", "优先成本，性能其次");

        private final String displayName;
        private final String description;

        CostSensitivity(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }

    /**
     * 响应速度偏好枚举
     */
    public enum SpeedPreference {
        FAST("快速", "优先响应速度"),
        BALANCED("平衡", "平衡速度和质量"),
        QUALITY("质量", "优先输出质量");

        private final String displayName;
        private final String description;

        SpeedPreference(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
    }

    /**
     * 任务模型偏好配置
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class TaskModelPreference {
        
        /**
         * 任务类型
         */
        private String taskType;
        
        /**
         * 首选提供商
         */
        private String preferredProvider;
        
        /**
         * 首选模型层级
         */
        private String preferredTier;
        
        /**
         * 首选具体模型
         */
        private String preferredModel;
        
        /**
         * 备选模型列表（按优先级排序）
         */
        private List<String> fallbackModels;
        
        /**
         * 任务特定的模型参数
         */
        private Map<String, Object> modelParameters;
    }

    /**
     * 模型使用统计
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class ModelUsageStats {
        
        /**
         * 总请求次数
         */
        @Builder.Default
        private Long totalRequests = 0L;
        
        /**
         * 各提供商使用次数统计
         */
        private Map<String, Long> providerUsageCount;
        
        /**
         * 各模型使用次数统计
         */
        private Map<String, Long> modelUsageCount;
        
        /**
         * 各任务类型使用次数统计
         */
        private Map<String, Long> taskTypeUsageCount;
        
        /**
         * 总消费金额（美元）
         */
        @Builder.Default
        private Double totalCost = 0.0;
        
        /**
         * 各提供商消费统计
         */
        private Map<String, Double> providerCostStats;
        
        /**
         * 平均响应时间（毫秒）
         */
        @Builder.Default
        private Double averageResponseTime = 0.0;
        
        /**
         * 用户满意度评分（1-5分）
         */
        @Builder.Default
        private Double satisfactionScore = 0.0;
        
        /**
         * 统计数据最后更新时间
         */
        private LocalDateTime lastUpdated;
    }

    /**
     * 获取首选模型层级枚举
     * 
     * @return 模型层级枚举
     */
    public ModelTierEnum getPreferredTierEnum() {
        return ModelTierEnum.fromTierOrDefault(preferredTier, ModelTierEnum.BASIC);
    }

    /**
     * 获取首选提供商枚举
     * 
     * @return 提供商枚举
     */
    public ProviderTypeEnum getPreferredProviderEnum() {
        return ProviderTypeEnum.fromProvider(preferredProvider);
    }

    /**
     * 检查指定提供商是否被禁用
     * 
     * @param provider 提供商标识符
     * @return 是否被禁用
     */
    public boolean isProviderDisabled(String provider) {
        return disabledProviders != null && disabledProviders.contains(provider);
    }

    /**
     * 检查指定模型是否被禁用
     * 
     * @param model 模型标识符
     * @return 是否被禁用
     */
    public boolean isModelDisabled(String model) {
        return disabledModels != null && disabledModels.contains(model);
    }

    /**
     * 获取指定任务类型的模型偏好
     * 
     * @param taskType 任务类型
     * @return 任务模型偏好，如果不存在则返回null
     */
    public TaskModelPreference getTaskModelPreference(String taskType) {
        return taskModelMappings != null ? taskModelMappings.get(taskType) : null;
    }

    /**
     * 更新最后修改时间
     */
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 初始化偏好设置
     */
    public void initialize() {
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        updatedAt = now;
        
        if (usageStats == null) {
            usageStats = ModelUsageStats.builder()
                .lastUpdated(now)
                .build();
        }
    }
}
