package com.yy.hd.model.config;

import com.yy.hd.model.enums.ModelTierEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 大模型提供商配置类
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelProviderConfig {

    /**
     * 提供商名称
     */
    private String name;

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * API密钥列表，支持多个密钥进行负载均衡
     */
    private List<String> apiKeys;

    /**
     * 模型配置映射
     * key: 模型层级（basic, advanced）
     * value: 该层级下的模型列表
     */
    private Map<String, List<ModelConfig>> models;

    /**
     * 获取基础模型列表
     * 
     * @return 基础模型列表
     */
    public List<ModelConfig> getBasicModels() {
        return models != null ? models.get(ModelTierEnum.BASIC.getTier()) : null;
    }

    /**
     * 获取高级模型列表
     * 
     * @return 高级模型列表
     */
    public List<ModelConfig> getAdvancedModels() {
        return models != null ? models.get(ModelTierEnum.ADVANCED.getTier()) : null;
    }

    /**
     * 根据层级获取模型列表
     * 
     * @param tier 模型层级
     * @return 模型列表
     */
    public List<ModelConfig> getModelsByTier(String tier) {
        return models != null ? models.get(tier) : null;
    }

    /**
     * 检查是否有可用的API密钥
     * 
     * @return 是否有可用的API密钥
     */
    public boolean hasApiKeys() {
        return apiKeys != null && !apiKeys.isEmpty();
    }

    /**
     * 获取API密钥数量
     * 
     * @return API密钥数量
     */
    public int getApiKeyCount() {
        return apiKeys != null ? apiKeys.size() : 0;
    }

}
