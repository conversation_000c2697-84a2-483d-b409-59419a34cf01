package com.yy.hd.model.enums;

import lombok.Getter;

/**
 * 大模型提供商类型枚举
 * 定义支持的各种大模型提供商
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Getter
public enum ProviderTypeEnum {

    /**
     * OpenAI
     */
    OPENAI("openai", "OpenAI", "OpenAI GPT系列模型", "https://api.openai.com", true),

    /**
     * Mistral AI
     */
    MISTRAL("mistral", "Mistral AI", "Mistral系列开源模型", "https://api.mistral.ai", true),

    /**
     * Anthropic Claude
     */
    CLAUDE("claude", "Anthropic Claude", "Claude系列对话模型", "https://api.anthropic.com", true),

    /**
     * 智谱AI
     */
    ZHIPU("zhipu", "智谱AI", "智谱GLM系列模型", "https://open.bigmodel.cn/api/paas/v4", true),

    /**
     * 百度文心一言
     */
    BAIDU("baidu", "百度文心", "百度文心一言系列模型", "https://aip.baidubce.com", true),

    /**
     * 阿里通义千问
     */
    ALIBABA("alibaba", "阿里通义", "阿里通义千问系列模型", "https://dashscope.aliyuncs.com", true),

    /**
     * 腾讯混元
     */
    TENCENT("tencent", "腾讯混元", "腾讯混元系列模型", "https://hunyuan.tencent.com", true),

    /**
     * 字节豆包
     */
    BYTEDANCE("bytedance", "字节豆包", "字节跳动豆包系列模型", "https://ark.cn-beijing.volces.com", true),

    /**
     * Google Gemini
     */
    GOOGLE("google", "Google Gemini", "Google Gemini系列模型", "https://generativelanguage.googleapis.com", true),

    /**
     * Microsoft Azure OpenAI
     */
    AZURE("azure", "Azure OpenAI", "Microsoft Azure OpenAI服务", "https://api.cognitive.microsoft.com", true),

    /**
     * Cohere
     */
    COHERE("cohere", "Cohere", "Cohere系列模型", "https://api.cohere.ai", false),

    /**
     * Hugging Face
     */
    HUGGINGFACE("huggingface", "Hugging Face", "Hugging Face模型服务", "https://api-inference.huggingface.co", false),

    /**
     * 自定义提供商
     */
    CUSTOM("custom", "自定义", "自定义模型提供商", "", false);

    /**
     * 提供商标识符
     */
    private final String provider;

    /**
     * 提供商显示名称
     */
    private final String displayName;

    /**
     * 提供商描述
     */
    private final String description;

    /**
     * 默认API基础URL
     */
    private final String defaultBaseUrl;

    /**
     * 是否为主流提供商
     */
    private final Boolean mainstream;

    ProviderTypeEnum(String provider, String displayName, String description, String defaultBaseUrl, Boolean mainstream) {
        this.provider = provider;
        this.displayName = displayName;
        this.description = description;
        this.defaultBaseUrl = defaultBaseUrl;
        this.mainstream = mainstream;
    }

    /**
     * 根据提供商标识符获取枚举值
     * 
     * @param provider 提供商标识符
     * @return 对应的枚举值，如果不存在则返回null
     */
    public static ProviderTypeEnum fromProvider(String provider) {
        if (provider == null || provider.trim().isEmpty()) {
            return null;
        }
        
        for (ProviderTypeEnum providerEnum : values()) {
            if (providerEnum.provider.equalsIgnoreCase(provider.trim())) {
                return providerEnum;
            }
        }
        return null;
    }

    /**
     * 根据提供商标识符获取枚举值，如果不存在则返回默认值
     * 
     * @param provider 提供商标识符
     * @param defaultProvider 默认值
     * @return 对应的枚举值或默认值
     */
    public static ProviderTypeEnum fromProviderOrDefault(String provider, ProviderTypeEnum defaultProvider) {
        ProviderTypeEnum result = fromProvider(provider);
        return result != null ? result : defaultProvider;
    }

    /**
     * 检查给定的提供商标识符是否有效
     * 
     * @param provider 提供商标识符
     * @return 是否有效
     */
    public static boolean isValidProvider(String provider) {
        return fromProvider(provider) != null;
    }

    /**
     * 获取所有提供商标识符
     * 
     * @return 提供商标识符数组
     */
    public static String[] getAllProviders() {
        ProviderTypeEnum[] values = values();
        String[] providers = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            providers[i] = values[i].provider;
        }
        return providers;
    }

    /**
     * 获取所有主流提供商
     * 
     * @return 主流提供商数组
     */
    public static ProviderTypeEnum[] getMainstreamProviders() {
        return java.util.Arrays.stream(values())
            .filter(provider -> Boolean.TRUE.equals(provider.mainstream))
            .toArray(ProviderTypeEnum[]::new);
    }

    /**
     * 获取所有主流提供商的标识符
     * 
     * @return 主流提供商标识符数组
     */
    public static String[] getMainstreamProviderNames() {
        return java.util.Arrays.stream(getMainstreamProviders())
            .map(ProviderTypeEnum::getProvider)
            .toArray(String[]::new);
    }

    /**
     * 检查是否为主流提供商
     * 
     * @return 是否为主流提供商
     */
    public boolean isMainstream() {
        return Boolean.TRUE.equals(mainstream);
    }

    /**
     * 检查是否为自定义提供商
     * 
     * @return 是否为自定义提供商
     */
    public boolean isCustom() {
        return this == CUSTOM;
    }

    /**
     * 检查是否需要API密钥
     * 
     * @return 是否需要API密钥
     */
    public boolean requiresApiKey() {
        // 大部分提供商都需要API密钥，除了一些特殊情况
        return this != CUSTOM;
    }

    @Override
    public String toString() {
        return String.format("ProviderTypeEnum{provider='%s', displayName='%s', description='%s', defaultBaseUrl='%s', mainstream=%s}", 
            provider, displayName, description, defaultBaseUrl, mainstream);
    }
}
