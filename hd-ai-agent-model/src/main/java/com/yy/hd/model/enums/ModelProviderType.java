package com.yy.hd.model.enums;

public enum ModelProviderType {

    MISTRAL("mistral");

    private String provider;

    ModelProviderType(String provider) {
        this.provider = provider;
    }

    public String getProvider() {
        return provider;
    }

    public static ModelProviderType fromProvider(String provider) {
        try {
            return ModelProviderType.valueOf(provider.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid provider: " + provider);
        }
    }
}
