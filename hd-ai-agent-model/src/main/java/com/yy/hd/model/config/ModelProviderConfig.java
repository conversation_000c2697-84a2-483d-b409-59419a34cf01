package com.yy.hd.model.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 大模型提供商配置类
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelProviderConfig {

    /**
     * 提供商名称
     */
    private String name;

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * API密钥列表，支持多个密钥进行负载均衡
     */
    private List<String> apiKeys;

    /**
     * 模型配置映射
     * key: 模型层级（basic, advanced）
     * value: 该层级下的模型列表
     */
    private Map<String, List<ModelConfig>> models;

    /**
     * 获取基础模型列表
     * 
     * @return 基础模型列表
     */
    public List<ModelConfig> getBasicModels() {
        return models != null ? models.get("basic") : null;
    }

    /**
     * 获取高级模型列表
     * 
     * @return 高级模型列表
     */
    public List<ModelConfig> getAdvancedModels() {
        return models != null ? models.get("advanced") : null;
    }

    /**
     * 根据层级获取模型列表
     * 
     * @param tier 模型层级
     * @return 模型列表
     */
    public List<ModelConfig> getModelsByTier(String tier) {
        return models != null ? models.get(tier) : null;
    }

    /**
     * 检查是否有可用的API密钥
     * 
     * @return 是否有可用的API密钥
     */
    public boolean hasApiKeys() {
        return apiKeys != null && !apiKeys.isEmpty();
    }

    /**
     * 获取API密钥数量
     * 
     * @return API密钥数量
     */
    public int getApiKeyCount() {
        return apiKeys != null ? apiKeys.size() : 0;
    }

    /**
     * 检查提供商是否可用
     * 提供商可用的条件：启用状态 && 有API密钥 && 有模型配置
     * 
     * @return 是否可用
     */
    public boolean isAvailable() {
        return enabled != null && enabled 
            && hasApiKeys() 
            && models != null && !models.isEmpty();
    }

    /**
     * 获取指定层级的启用模型数量
     * 
     * @param tier 模型层级
     * @return 启用模型数量
     */
    public long getEnabledModelCount(String tier) {
        List<ModelConfig> tierModels = getModelsByTier(tier);
        if (tierModels == null) {
            return 0;
        }
        return tierModels.stream()
            .filter(model -> model.getEnabled() != null && model.getEnabled())
            .count();
    }

    /**
     * 获取所有启用的模型数量
     * 
     * @return 启用模型总数
     */
    public long getTotalEnabledModelCount() {
        if (models == null) {
            return 0;
        }
        return models.values().stream()
            .flatMap(List::stream)
            .filter(model -> model.getEnabled() != null && model.getEnabled())
            .count();
    }
}
