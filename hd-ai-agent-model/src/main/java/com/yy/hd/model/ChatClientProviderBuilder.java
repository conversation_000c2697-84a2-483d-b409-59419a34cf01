package com.yy.hd.model;

import com.yy.hd.model.properties.ModelProviderProperties;
import com.yy.hd.model.properties.ModelRoutingContext;
import com.yy.hd.model.properties.Routing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.api.Advisor;

import java.util.Arrays;
import java.util.List;

@Slf4j
public class ChatClientProviderBuilder {

    public static ChatClientProviderBuilder.Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private ModelRoutingContext modelRoutingContext;

        private List<Advisor> advisors;

        public Builder context(ModelRoutingContext modelRoutingContext) {
            this.modelRoutingContext = modelRoutingContext;
            return this;
        }

        public Builder advisors(List<Advisor> advisors) {
            this.advisors = advisors;
            return this;
        }

        public ChatClientProvider build() {
            return new ChatClientProviderBuilder()
                    .createChatClientProvider(modelRoutingContext, advisors);
        }
    }

    private ChatClientProvider createChatClientProvider(ModelRoutingContext modelRoutingContext, List<Advisor> advisors) {
        String defaultProvider = modelRoutingContext.getDefaultProvider();
        List<ModelProviderProperties> providers = modelRoutingContext.getProviders();
        Routing routing = modelRoutingContext.getRouting();
        String strategy = routing.getStrategy();
        try {
            ChatClientPool chatClientPool = new ChatClientPool(defaultProvider, providers, routing, advisors);
            return switch (RoutingStrategy.strategyOf(strategy)) {
                case ROUND_ROBIN -> new RoundRobinProvider(chatClientPool);
                case null -> throw new IllegalArgumentException("Unknown strategy: " + strategy);
            };
        } catch (Throwable e) {
            log.error("createChatClientProvider fail, strategy: {}", strategy, e);
            throw e;
        }
    }

    enum RoutingStrategy {

        ROUND_ROBIN("round-robin");

        private String strategy;

        RoutingStrategy(String strategy) {
            this.strategy = strategy;
        }

        public static RoutingStrategy strategyOf(String strategy) {
            return Arrays.stream(RoutingStrategy.values())
                    .filter(routingStrategy -> routingStrategy.strategy.equals(strategy))
                    .findFirst()
                    .orElse(null);
        }
    }
}
