package com.yy.hd.model;

import com.yy.hd.model.config.AiModelConfig;
import com.yy.hd.model.properties.ModelProviderProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.api.Advisor;

import java.util.List;
import java.util.Map;

@Slf4j
public class ChatClientProviderBuilder {

    public static ChatClientProviderBuilder.Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private AiModelConfig aiModelConfig;

        private List<Advisor> advisors;

        public Builder aiModelConfig(AiModelConfig aiModelConfig) {
            this.aiModelConfig = aiModelConfig;
            return this;
        }

        public Builder advisors(List<Advisor> advisors) {
            this.advisors = advisors;
            return this;
        }

        public ChatClientProvider build() {
            return new ChatClientProviderBuilder()
                    .createChatClientProvider(aiModelConfig, advisors);
        }
    }

    private ChatClientProvider createChatClientProvider(AiModelConfig aiModelConfig, List<Advisor> advisors) {
        String defaultProvider = aiModelConfig.getDefaultProvider();
        // key: 提供商标识（如 openai, mistral）
        Map<String, ModelProviderProperties> providers = aiModelConfig.getProviders();
        ChatClientPool chatClientPool = new ChatClientPool(providers, advisors);
        return new ApiKeyRoundRobinProvider(chatClientPool);
    }

}
