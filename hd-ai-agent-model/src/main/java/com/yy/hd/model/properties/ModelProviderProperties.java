package com.yy.hd.model.properties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 大模型提供商配置类
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelProviderProperties {

    /**
     * 提供商名称
     */
    private String name;

    /**
     * API基础URL
     */
    private String baseUrl;

    /**
     * API密钥列表，支持多个密钥进行负载均衡
     */
    private List<String> apiKeys;

    /**
     * 模型配置映射
     */
    private List<ModelProperties> models;

}
