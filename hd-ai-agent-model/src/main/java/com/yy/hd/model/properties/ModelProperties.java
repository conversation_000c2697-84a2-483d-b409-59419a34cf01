package com.yy.hd.model.properties;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 单个模型配置类
 * 支持基础模型和高级模型的详细配置
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ModelProperties {

    /**
     * 模型名称（API调用时使用的标识）
     */
    private String modelName;

    /**
     * 模型显示名称（用户界面显示）
     */
    private String displayName;

    /**
     * 模型描述
     */
    private String description;

}
