package com.yy.hd.model;

import com.yy.hd.model.properties.ModelRoutingContext;
import com.yy.hd.model.properties.ModelProvider;
import com.yy.hd.model.properties.Routing;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.api.Advisor;

import java.util.Arrays;
import java.util.List;
import java.util.function.Supplier;

@Slf4j
public class ChatClientSelectorBuilder {

    public static ChatClientSelectorBuilder.Builder builder() {
        return new Builder();
    }

    public static class Builder {

        private ModelRoutingContext modelRoutingContext;

        private List<Advisor> advisors;

        public Builder context(ModelRoutingContext modelRoutingContext) {
            this.modelRoutingContext = modelRoutingContext;
            return this;
        }

        public Builder advisors(List<Advisor> advisors) {
            this.advisors = advisors;
            return this;
        }

        public ChatClientProvider build() {
            return new ChatClientSelectorBuilder()
                    .createChatClientSelector(modelRoutingContext.getProviders(), modelRoutingContext.getRouting(), advisors);
        }
    }

    private ChatClientProvider createChatClientSelector(List<ModelProvider> providers, Routing routing, List<Advisor> advisors) {
        String strategy = routing.getStrategy();
        try {
            return switch (RoutingStrategy.strategyOf(strategy)) {
                case ROUND_ROBIN -> new RoundRobinProvider(providers, routing, advisors);
                case WEIGHTED_ROUND_ROBIN -> new WeightedRoundRobinProvider(providers, routing, advisors);
            };
        } catch (Throwable e) {
            log.error("createChatClientSelector fail, unknown strategy: {}", strategy, e);
            throw new IllegalArgumentException("Unknown strategy: " + strategy);
        }
    }

    enum RoutingStrategy {

        ROUND_ROBIN("round-robin"),
        WEIGHTED_ROUND_ROBIN("weighted-round-robin");

        private String strategy;

        RoutingStrategy(String strategy) {
            this.strategy = strategy;
        }

        public static RoutingStrategy strategyOf(String strategy) throws Throwable {
            return Arrays.stream(RoutingStrategy.values())
                    .filter(routingStrategy -> routingStrategy.strategy.equals(strategy))
                    .findFirst()
                    .orElseThrow(new Supplier<Throwable>() {
                        @Override
                        public Throwable get() {
                            return new RuntimeException("Invalid routing strategy: " + strategy);
                        }
                    });
        }
    }
}
