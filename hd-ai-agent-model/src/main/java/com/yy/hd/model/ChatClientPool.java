package com.yy.hd.model;

import com.yy.hd.model.properties.ModelProviderProperties;
import com.yy.hd.model.properties.Retry;
import com.yy.hd.model.properties.Routing;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.backoff.BackOffPolicyBuilder;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChatClientPool {

    private static final String DEFAULT_COMPLETIONS_PATH = "/v1/chat/completions";

    private static final ClientHttpRequestFactorySettings REQUEST_FACTORY_SETTINGS = ClientHttpRequestFactorySettings.defaults()
            .withConnectTimeout(Duration.ofSeconds(5))
            .withReadTimeout(Duration.ofSeconds(60));

    private static final HttpClient DEFAULT_HTTP_CLIENT = HttpClient.create()
            .responseTimeout(Duration.ofSeconds(60));

    private final Map<String, List<ChatClientHolder>> chatClientPool;

    private final String defaultProvider;

    public ChatClientPool(String defaultProvider, List<ModelProviderProperties> providers, Routing routing, List<Advisor> advisors) {
        Assert.hasText(defaultProvider, "defaultProvider must not be null");
        this.defaultProvider = defaultProvider;
        Retry retry = routing.getRetry();
        BackOffPolicy backOffPolicy = BackOffPolicyBuilder.newBuilder()
                .delay(retry.getBackoff().getInitialInterval())
                .multiplier(retry.getBackoff().getMultiplier())
                .maxDelay(retry.getBackoff().getMaxInterval())
                .build();
        RetryTemplate retryTemplate = RetryTemplate.builder()
                .maxAttempts(retry.getMaxAttempts())
                .customBackoff(backOffPolicy)
                // NonTransientAiException 是 Spring AI 框架中异常体系的一部分，它用于封装与 AI 服务交互过程中发生的不可恢复的错误
                .notRetryOn(NonTransientAiException.class)
                .build();
        Map<String, List<ChatClientHolder>> chatClientMap = new HashMap<>();
        for (ModelProviderProperties modelProviderProperties : providers) {
            List<ChatClientHolder> chatClients = new ArrayList<>();
            String baseUrl = modelProviderProperties.getBaseUrl();
            List<String> apiKeys = modelProviderProperties.getApiKeys();
            for (String apiKey : apiKeys) {
                for (ModelProperties modelProperties : modelProviderProperties.getModelProperties()) {
                    ChatClient chatClient = buildChatClient(apiKey, baseUrl, modelProperties, advisors, retryTemplate);
                    chatClients.add(new ChatClientHolder(modelProperties, chatClient));
                }
            }
            chatClientMap.put(modelProviderProperties.getProvider(), Collections.unmodifiableList(chatClients));
        }
        this.chatClientPool = Collections.unmodifiableMap(chatClientMap);
    }

    public Map<String, List<ChatClientHolder>> getPool() {
        return chatClientPool;
    }

    public List<ChatClientHolder> getChatClients(String provider) {
        return chatClientPool.get(provider);
    }

    public String getDefaultProvider() {
        return defaultProvider;
    }

    private ChatClient buildChatClient(String apiKey, String baseUrl,
                                       String model, List<Advisor> advisors,
                                       RetryTemplate retryTemplate) {
        OpenAiApi openAiApi = OpenAiApi.builder()
                .apiKey(apiKey)
                .baseUrl(baseUrl)
                .restClientBuilder(RestClient.builder()
                        .requestFactory(ClientHttpRequestFactoryBuilder
                                .detect()
                                .build(REQUEST_FACTORY_SETTINGS)))
                .webClientBuilder(WebClient.builder()
                        .clientConnector(new ReactorClientHttpConnector(DEFAULT_HTTP_CLIENT)))
                .completionsPath(DEFAULT_COMPLETIONS_PATH)
                .build();
        var openAiChatOptions = OpenAiChatOptions.builder()
                .model(model)
                .build();
        ChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .retryTemplate(retryTemplate)
                .observationRegistry(ObservationRegistry.create())
                .build();
        return ChatClient.builder(chatModel)
                .defaultAdvisors(advisors)
                .build();
    }
}
