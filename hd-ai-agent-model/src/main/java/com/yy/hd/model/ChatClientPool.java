package com.yy.hd.model;

import com.yy.hd.model.enums.ModelTierEnum;
import com.yy.hd.model.properties.ModelProperties;
import com.yy.hd.model.properties.ModelProviderProperties;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.boot.http.client.ClientHttpRequestFactoryBuilder;
import org.springframework.boot.http.client.ClientHttpRequestFactorySettings;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.backoff.BackOffPolicyBuilder;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;
import org.springframework.web.client.RestClient;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ChatClientPool {

    private static final String DEFAULT_COMPLETIONS_PATH = "/v1/chat/completions";

    private static final ClientHttpRequestFactorySettings REQUEST_FACTORY_SETTINGS = ClientHttpRequestFactorySettings.defaults()
            .withConnectTimeout(Duration.ofSeconds(5))
            .withReadTimeout(Duration.ofSeconds(60));

    private static final HttpClient DEFAULT_HTTP_CLIENT = HttpClient.create()
            .responseTimeout(Duration.ofSeconds(60));

    /**
     * key: 提供商标识
     * value: key: 模型层级 value: 该层级下的模型列表
     */
    private final Map<String, Map<String, List<ChatClientWrapper>>> chatClientPool;

    private final Map<String, List<String>> providerApiKeys;

    public ChatClientPool(Map<String, ModelProviderProperties> providers, List<Advisor> advisors) {
        BackOffPolicy backOffPolicy = BackOffPolicyBuilder.newBuilder()
                .delay(1000)
                .multiplier(1)
                .maxDelay(3000)
                .build();
        RetryTemplate retryTemplate = RetryTemplate.builder()
                .maxAttempts(1)
                .customBackoff(backOffPolicy)
                // NonTransientAiException 是 Spring AI 框架中异常体系的一部分，它用于封装与 AI 服务交互过程中发生的不可恢复的错误
                .notRetryOn(NonTransientAiException.class)
                .build();
        Map<String, Map<String, List<ChatClientWrapper>>> providerClientPool = new HashMap<>();
        Map<String, List<String>> apiKeysMap = new HashMap<>();
        for (Map.Entry<String, ModelProviderProperties> providerPropertiesEntry : providers.entrySet()) {
            ModelProviderProperties modelProviderProperties = providerPropertiesEntry.getValue();
            String providerName = providerPropertiesEntry.getKey();
            List<String> apiKeys = modelProviderProperties.getApiKeys();
            apiKeysMap.put(providerName, apiKeys);
            Map<String, List<ChatClientWrapper>> tiredClientMap = new HashMap<>();
            String baseUrl = modelProviderProperties.getBaseUrl();
            Map<String, List<ModelProperties>> models = modelProviderProperties.getModels();
            for (Map.Entry<String, List<ModelProperties>> entry : models.entrySet()) {
                String tier = entry.getKey();
                List<ChatClientWrapper> chatClients = new ArrayList<>();
                List<ModelProperties> tierModels = entry.getValue();
                for (ModelProperties modelProperties : tierModels) {
                    String modelName = modelProperties.getModelName();
                    chatClients.addAll(createChatClientHolders(apiKeys, baseUrl, modelName, advisors, retryTemplate));
                }
                tiredClientMap.put(tier, Collections.unmodifiableList(chatClients));
            }
            providerClientPool.put(providerName, tiredClientMap);
        }
        this.chatClientPool = Collections.unmodifiableMap(providerClientPool);
        this.providerApiKeys = Collections.unmodifiableMap(apiKeysMap);
    }

    public Map<String, List<ChatClientWrapper>> getPool(String provider) {
        return chatClientPool.get(provider);
    }

    public String getDefaultProvider() {
        return defaultProvider;
    }

    public List<String> getProviders() {
        return chatClientPool.keySet()
                .stream()
                .toList();
    }

    public List<String> getApiKeys(String provider) {
        return providerApiKeys.get(provider);
    }

    private List<ChatClientWrapper> createChatClientHolders(List<String> apiKeys, String baseUrl,
                                                            String model, List<Advisor> advisors,
                                                            RetryTemplate retryTemplate) {
        return apiKeys.stream()
                .map(apiKey -> {
                    OpenAiApi openAiApi = OpenAiApi.builder()
                            .apiKey(apiKey)
                            .baseUrl(baseUrl)
                            .restClientBuilder(RestClient.builder()
                                    .requestFactory(ClientHttpRequestFactoryBuilder
                                            .detect()
                                            .build(REQUEST_FACTORY_SETTINGS)))
                            .webClientBuilder(WebClient.builder()
                                    .clientConnector(new ReactorClientHttpConnector(DEFAULT_HTTP_CLIENT)))
                            .completionsPath(DEFAULT_COMPLETIONS_PATH)
                            .build();
                    var openAiChatOptions = OpenAiChatOptions.builder()
                            .model(model)
                            .build();
                    ChatModel chatModel = OpenAiChatModel.builder()
                            .openAiApi(openAiApi)
                            .defaultOptions(openAiChatOptions)
                            .retryTemplate(retryTemplate)
                            .observationRegistry(ObservationRegistry.create())
                            .build();
                    ChatClient chatClient = ChatClient.builder(chatModel)
                            .defaultAdvisors(advisors)
                            .build();
                    return new ChatClientWrapper(model, apiKey, ModelTierEnum.BASIC, chatClient);
                })
                .toList();
    }
}
