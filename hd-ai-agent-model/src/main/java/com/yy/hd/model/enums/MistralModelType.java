package com.yy.hd.model.enums;

import lombok.Getter;

@Getter
public enum MistralModelType {

    /**
     * 基础模型
     */
    MISTRAL_SMALL("mistral-small-latest", "basic"),
    /**
     * 高级模型
     */
    MISTRAL_LARGE("mistral-large-latest", "advanced");

    private final String model;
    private final String tier;

    MistralModelType(String model, String tier) {
        this.model = model;
        this.tier = tier;
    }

}
