package com.yy.hd.model;

import com.yy.hd.model.properties.Model;
import com.yy.hd.model.properties.ModelProvider;
import com.yy.hd.model.properties.Retry;
import com.yy.hd.model.properties.Routing;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.backoff.BackOffPolicyBuilder;
import org.springframework.retry.support.RetryTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class WeightedRoundRobinSelector extends AbstractChatClientSelector implements ChatClientSelector {

    private static final List<Integer> cumulativeWeights = new ArrayList<>();
    private static int totalWeight = 0;

    public WeightedRoundRobinSelector(List<ModelProvider> providers, Routing routing, List<Advisor> advisors) {
        Retry retry = routing.getRetry();
        BackOffPolicy backOffPolicy = BackOffPolicyBuilder.newBuilder()
                .delay(retry.getBackoff().getInitialInterval())
                .multiplier(retry.getBackoff().getMultiplier())
                .maxDelay(retry.getBackoff().getMaxInterval())
                .build();
        RetryTemplate retryTemplate = RetryTemplate.builder()
                .maxAttempts(retry.getMaxAttempts())
                .customBackoff(backOffPolicy)
                // NonTransientAiException 是 Spring AI 框架中异常体系的一部分，它用于封装与 AI 服务交互过程中发生的不可恢复的错误
                .notRetryOn(NonTransientAiException.class)
                .build();
        // 初始化模型客户端和权重
        for (ModelProvider provider : providers) {
            String apiKey = provider.getApiKey();
            String baseUrl = provider.getBaseUrl();
            for (Model model : provider.getModels()) {
                if (model.getWeight() > 0) {
                    buildChatClient(apiKey, baseUrl, model, retryTemplate, advisors);
                    int weight = model.getWeight();
                    totalWeight += weight;
                    cumulativeWeights.add(totalWeight);
                }
            }
        }
    }

    @Override
    public ChatClient selectChatClient() {
        List<ChatClient> chatClients = getChatClients();
        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        for (int i = 0; i < cumulativeWeights.size(); i++) {
            if (randomValue < cumulativeWeights.get(i)) {
                return chatClients.get(i);
            }
        }
        return chatClients.getFirst();
    }

}
