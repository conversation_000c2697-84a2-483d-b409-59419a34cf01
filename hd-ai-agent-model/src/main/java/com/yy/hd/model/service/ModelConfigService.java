package com.yy.hd.model.service;

import com.yy.hd.model.config.ModelConfig;
import com.yy.hd.model.config.ModelProviderConfig;
import com.yy.hd.model.enums.ModelTierEnum;
import com.yy.hd.model.properties.AiModelProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 模型配置服务类
 * 提供模型配置的查询、管理和选择功能
 * 
 * <AUTHOR> Agent
 * @since 1.0
 */
@Slf4j
@Service
public class ModelConfigService {

    private final AiModelProperties aiModelProperties;

    @Autowired
    public ModelConfigService(AiModelProperties aiModelProperties) {
        this.aiModelProperties = aiModelProperties;
    }

    /**
     * 获取所有启用的提供商
     * 
     * @return 启用的提供商列表
     */
    public List<ModelProviderConfig> getEnabledProviders() {
        if (aiModelProperties.getModels() == null || aiModelProperties.getModels().getProviders() == null) {
            return List.of();
        }
        
        return aiModelProperties.getModels().getProviders().values().stream()
            .filter(provider -> Boolean.TRUE.equals(provider.getEnabled()))
            .collect(Collectors.toList());
    }

    /**
     * 根据提供商名称获取提供商配置
     * 
     * @param providerName 提供商名称
     * @return 提供商配置，如果不存在则返回空
     */
    public Optional<ModelProviderConfig> getProviderConfig(String providerName) {
        if (aiModelProperties.getModels() == null || aiModelProperties.getModels().getProviders() == null) {
            return Optional.empty();
        }
        
        ModelProviderConfig provider = aiModelProperties.getModels().getProviders().get(providerName);
        return Optional.ofNullable(provider);
    }

    /**
     * 获取指定提供商和层级的模型列表
     * 
     * @param providerName 提供商名称
     * @param tier 模型层级
     * @return 模型列表
     */
    public List<ModelConfig> getModels(String providerName, String tier) {
        return getProviderConfig(providerName)
            .map(provider -> provider.getModelsByTier(tier))
            .orElse(List.of());
    }

    /**
     * 获取指定提供商的基础模型列表
     * 
     * @param providerName 提供商名称
     * @return 基础模型列表
     */
    public List<ModelConfig> getBasicModels(String providerName) {
        return getModels(providerName, ModelTierEnum.BASIC.getTier());
    }

    /**
     * 获取指定提供商的高级模型列表
     * 
     * @param providerName 提供商名称
     * @return 高级模型列表
     */
    public List<ModelConfig> getAdvancedModels(String providerName) {
        return getModels(providerName, ModelTierEnum.ADVANCED.getTier());
    }

    /**
     * 根据用户偏好选择最适合的模型
     * 
     * @param userPreference 用户偏好设置
     * @param taskType 任务类型（可选）
     * @return 选择的模型配置
     */
    public Optional<ModelSelectionResult> selectBestModel(UserModelPreference userPreference, String taskType) {
        // 1. 检查任务特定的偏好设置
        if (taskType != null && userPreference.getTaskModelPreference(taskType) != null) {
            var taskPreference = userPreference.getTaskModelPreference(taskType);
            var result = selectModelByPreference(taskPreference.getPreferredProvider(), 
                taskPreference.getPreferredTier(), taskPreference.getPreferredModel());
            if (result.isPresent()) {
                return result;
            }
        }

        // 2. 使用用户的通用偏好设置
        return selectModelByPreference(userPreference.getPreferredProvider(), 
            userPreference.getPreferredTier(), userPreference.getPreferredModel());
    }

    /**
     * 根据偏好参数选择模型
     * 
     * @param providerName 提供商名称
     * @param tier 模型层级
     * @param modelName 具体模型名称（可选）
     * @return 选择的模型配置
     */
    public Optional<ModelSelectionResult> selectModelByPreference(String providerName, String tier, String modelName) {
        var providerConfig = getProviderConfig(providerName);
        if (providerConfig.isEmpty() || !providerConfig.get().isAvailable()) {
            log.warn("Provider {} is not available", providerName);
            return selectFallbackModel(tier);
        }

        var models = getModels(providerName, tier);
        if (models.isEmpty()) {
            log.warn("No models found for provider {} and tier {}", providerName, tier);
            return selectFallbackModel(tier);
        }

        // 如果指定了具体模型，尝试找到它
        if (modelName != null && !modelName.trim().isEmpty()) {
            var specificModel = models.stream()
                .filter(model -> model.getModelName().equals(modelName) && model.isAvailable())
                .findFirst();
            if (specificModel.isPresent()) {
                return Optional.of(new ModelSelectionResult(providerConfig.get(), specificModel.get(), "specific_model"));
            }
        }

        // 选择第一个可用的模型
        var availableModel = models.stream()
            .filter(ModelConfig::isAvailable)
            .findFirst();
        
        if (availableModel.isPresent()) {
            return Optional.of(new ModelSelectionResult(providerConfig.get(), availableModel.get(), "first_available"));
        }

        return selectFallbackModel(tier);
    }

    /**
     * 选择备用模型
     * 
     * @param preferredTier 首选层级
     * @return 备用模型配置
     */
    private Optional<ModelSelectionResult> selectFallbackModel(String preferredTier) {
        // 从默认提供商中选择备用模型
        String defaultProvider = aiModelProperties.getDefaultProvider();
        if (defaultProvider == null) {
            log.error("No default provider configured");
            return Optional.empty();
        }

        var models = getModels(defaultProvider, preferredTier);
        if (models.isEmpty()) {
            // 如果首选层级没有模型，尝试其他层级
            String fallbackTier = ModelTierEnum.BASIC.getTier().equals(preferredTier) 
                ? ModelTierEnum.ADVANCED.getTier() 
                : ModelTierEnum.BASIC.getTier();
            models = getModels(defaultProvider, fallbackTier);
        }

        var availableModel = models.stream()
            .filter(ModelConfig::isAvailable)
            .findFirst();

        if (availableModel.isPresent()) {
            var providerConfig = getProviderConfig(defaultProvider).orElse(null);
            if (providerConfig != null) {
                return Optional.of(new ModelSelectionResult(providerConfig, availableModel.get(), "fallback"));
            }
        }

        log.error("No fallback model available");
        return Optional.empty();
    }

    /**
     * 获取所有可用的模型统计信息
     * 
     * @return 模型统计信息
     */
    public ModelStats getModelStats() {
        var enabledProviders = getEnabledProviders();
        
        long totalProviders = enabledProviders.size();
        long totalBasicModels = enabledProviders.stream()
            .mapToLong(provider -> provider.getEnabledModelCount(ModelTierEnum.BASIC.getTier()))
            .sum();
        long totalAdvancedModels = enabledProviders.stream()
            .mapToLong(provider -> provider.getEnabledModelCount(ModelTierEnum.ADVANCED.getTier()))
            .sum();
        
        Map<String, Long> providerModelCounts = enabledProviders.stream()
            .collect(Collectors.toMap(
                ModelProviderConfig::getName,
                ModelProviderConfig::getTotalEnabledModelCount
            ));

        return new ModelStats(totalProviders, totalBasicModels, totalAdvancedModels, 
            totalBasicModels + totalAdvancedModels, providerModelCounts);
    }

    /**
     * 检查配置是否有效
     * 
     * @return 配置验证结果
     */
    public ConfigValidationResult validateConfiguration() {
        var issues = new java.util.ArrayList<String>();
        
        if (!aiModelProperties.isValid()) {
            issues.add("AI model properties configuration is invalid");
        }
        
        if (aiModelProperties.getEnabledProviderCount() == 0) {
            issues.add("No enabled providers found");
        }
        
        String defaultProvider = aiModelProperties.getDefaultProvider();
        if (defaultProvider != null && !aiModelProperties.isProviderEnabled(defaultProvider)) {
            issues.add("Default provider '" + defaultProvider + "' is not enabled");
        }
        
        // 检查每个提供商的配置
        getEnabledProviders().forEach(provider -> {
            if (!provider.hasApiKeys()) {
                issues.add("Provider '" + provider.getName() + "' has no API keys configured");
            }
            if (provider.getTotalEnabledModelCount() == 0) {
                issues.add("Provider '" + provider.getName() + "' has no enabled models");
            }
        });
        
        return new ConfigValidationResult(issues.isEmpty(), issues);
    }

    /**
     * 模型选择结果
     */
    public record ModelSelectionResult(
        ModelProviderConfig provider,
        ModelConfig model,
        String selectionReason
    ) {}

    /**
     * 模型统计信息
     */
    public record ModelStats(
        long totalProviders,
        long totalBasicModels,
        long totalAdvancedModels,
        long totalModels,
        Map<String, Long> providerModelCounts
    ) {}

    /**
     * 配置验证结果
     */
    public record ConfigValidationResult(
        boolean isValid,
        List<String> issues
    ) {}
}
