package com.yy.hd.model;

import com.yy.hd.model.properties.Model;
import com.yy.hd.model.properties.ModelProvider;
import com.yy.hd.model.properties.Retry;
import com.yy.hd.model.properties.Routing;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.retry.backoff.BackOffPolicy;
import org.springframework.retry.backoff.BackOffPolicyBuilder;
import org.springframework.retry.support.RetryTemplate;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

public class RoundRobinSelector extends AbstractChatClientSelector implements ChatClientSelector {

    private static final AtomicInteger roundRobinIndex = new AtomicInteger(0);

    public RoundRobinSelector(List<ModelProvider> providers, Routing routing, List<Advisor> advisors) {
        Retry retry = routing.getRetry();
        BackOffPolicy backOffPolicy = BackOffPolicyBuilder.newBuilder()
                .delay(retry.getBackoff().getInitialInterval())
                .multiplier(retry.getBackoff().getMultiplier())
                .maxDelay(retry.getBackoff().getMaxInterval())
                .build();
        RetryTemplate retryTemplate = RetryTemplate.builder()
                .maxAttempts(retry.getMaxAttempts())
                .customBackoff(backOffPolicy)
                // NonTransientAiException 是 Spring AI 框架中异常体系的一部分，它用于封装与 AI 服务交互过程中发生的不可恢复的错误
                .notRetryOn(NonTransientAiException.class)
                .build();
        // 初始化模型客户端和权重
        for (ModelProvider provider : providers) {
            String apiKey = provider.getApiKey();
            String baseUrl = provider.getBaseUrl();
            String completionsPath = provider.getCompletionsPath();
            for (Model model : provider.getModels()) {
                if (model.getWeight() > 0) {
                    buildChatClient(apiKey, baseUrl, completionsPath, model, retryTemplate, advisors);
                }
            }
        }
    }

    @Override
    public ChatClient selectChatClient() {
        List<ChatClient> chatClients = getChatClients();
        int pos = roundRobinIndex.getAndUpdate(i -> (i + 1) % chatClients.size());
        return chatClients.get(pos);
    }

}
