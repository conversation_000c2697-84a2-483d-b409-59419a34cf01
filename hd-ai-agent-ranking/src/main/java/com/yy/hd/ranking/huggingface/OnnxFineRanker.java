package com.yy.hd.ranking.huggingface;

import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.output.Response;
import dev.langchain4j.model.scoring.onnx.OnnxScoringModel;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.ai.document.Document;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.IntStream;

/**
 * 精排：对粗排结果进一步排序
 */
public class OnnxFineRanker {

    private final OnnxScoringModel scoringModel;

    public OnnxFineRanker(String onnxModelUri, String tokenizerUri) throws OrtException {
        int numThreads = Runtime.getRuntime().availableProcessors();
        String cpuLimit = System.getProperty("MY_CPU_LIMIT");
        if (StringUtils.hasText(cpuLimit)) {
            numThreads = Integer.parseInt(cpuLimit);
        }
        OrtSession.SessionOptions sessionOptions = new OrtSession.SessionOptions();
        sessionOptions.setInterOpNumThreads(numThreads); // 操作间并行（例如 Transformer 的多头注意力机制）
        sessionOptions.setIntraOpNumThreads(1); // 操作内单线程，减少亲和性问题
        sessionOptions.addConfigEntry("session.intra_op.allow_spinning", "0"); // 禁用自旋
        // normalize=true，规范生成的相关性得分在0-1之间
        scoringModel = new OnnxScoringModel(onnxModelUri, sessionOptions, tokenizerUri, 510, true);
    }

    public List<Document> ranking(List<Document> documents, String query, double score, int limit) {
        if (CollectionUtils.isEmpty(documents)) {
            return Collections.emptyList();
        }
        List<TextSegment> segments = documents.stream()
                .map(document -> new TextSegment(document.getText(), new Metadata()))
                .toList();
        Response<List<Double>> response = scoringModel.scoreAll(segments, query);
        List<Double> scores = response.content();
        List<Document> unsortedDocuments = new ArrayList<>(documents.size());
        IntStream.range(0, documents.size())
                .forEach(i -> {
                    Document document = documents.get(i);
                    Document mutate = document.mutate()
                            .score(scores.get(i))
                            .build();
                    unsortedDocuments.add(mutate);
                });
        return unsortedDocuments.stream()
                .filter(document -> document.getScore() >= score)
                .sorted(Comparator.comparing(Document::getScore, Comparator.reverseOrder()))
                .limit(limit)
                .toList();
    }

}
