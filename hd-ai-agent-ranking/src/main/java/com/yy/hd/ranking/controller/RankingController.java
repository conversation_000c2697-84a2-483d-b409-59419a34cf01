package com.yy.hd.ranking.controller;

import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.ranking.dto.BM25SearchDTO;
import com.yy.hd.ranking.dto.DocumentDTO;
import com.yy.hd.ranking.dto.UpdateDocumentsDTO;
import com.yy.hd.ranking.dto.FineRankingDTO;
import com.yy.hd.ranking.dto.SimilaritySearchDTO;
import com.yy.hd.ranking.service.RankingService;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@AllArgsConstructor
@RestController
public class RankingController {

    @Resource
    private RankingService rankingService;

    @PostMapping(value = "/documents")
    public Mono<String> documents(@RequestBody UpdateDocumentsDTO req) {
        log.info("documents req:{}", JsonUtils.toJson(req));
        return Mono.just(req)
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(updateDocumentsDTO -> rankingService.documents(updateDocumentsDTO))
                .then(Mono.just("ok"));
    }

    @PostMapping(value = "/search/bm25")
    public Flux<DocumentDTO> bm25Search(@RequestBody BM25SearchDTO req) {
        log.info("bm25Search req:{}", JsonUtils.toJson(req));
        return Flux.just(req)
                .publishOn(Schedulers.boundedElastic())
                .flatMapIterable(rankingService::bm25Search);
    }

    @PostMapping(value = "/search/similarity")
    public Flux<DocumentDTO> similaritySearch(@RequestBody SimilaritySearchDTO req) {
        log.info("similaritySearch req:{}", JsonUtils.toJson(req));
        return Flux.just(req)
                .publishOn(Schedulers.boundedElastic())
                .flatMapIterable(rankingService::similaritySearch);
    }

    @PostMapping(value = "/ranking")
    public Flux<DocumentDTO> fineRanking(@RequestBody FineRankingDTO req) {
        log.info("fineRanking req:{}", JsonUtils.toJson(req));
        return Flux.just(req)
                .publishOn(Schedulers.boundedElastic())
                .flatMapIterable(rankingService::fineRanking);
    }
}
