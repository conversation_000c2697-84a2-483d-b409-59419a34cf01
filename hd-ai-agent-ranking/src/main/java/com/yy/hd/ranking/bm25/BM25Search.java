package com.yy.hd.ranking.bm25;

import com.hankcs.hanlp.seg.common.Term;
import com.hankcs.hanlp.tokenizer.NotionalTokenizer;
import com.yy.hd.ranking.dto.DocumentDTO;
import smile.nlp.relevance.BM25;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class BM25Search {
    // 语料库：文档列表
    private List<DocumentDTO> documents;
    // BM25 模型
    private BM25 bm25;
    // 文档词频统计
    private List<Map<String, Integer>> docTermFreqs;
    // 文档长度
    private List<Integer> docLengths;
    // 平均文档长度
    private double avgDocLength;
    // 包含某词的文档数
    private Map<String, Integer> termDocCount;
    // 语料库文档总数
    private int totalDocs;

    public BM25Search(List<DocumentDTO> documents) {
        this.bm25 = new BM25(1.2, 0.75, 1.0);
        this.documents = documents;
        this.docTermFreqs = new ArrayList<>();
        this.docLengths = new ArrayList<>();
        this.termDocCount = new HashMap<>();
        this.totalDocs = documents.size();
        preprocessDocuments();
    }

    // 预处理文档：分词、统计词频和文档长度
    private void preprocessDocuments() {
        for (DocumentDTO doc : documents) {
            // 分词
            List<Term> terms = NotionalTokenizer.segment(doc.text());
            String[] tokens = terms.stream().map(t -> t.word).toArray(String[]::new);
            // 统计词频
            Map<String, Integer> termFreq = new HashMap<>();
            for (String token : tokens) {
                if (!token.trim().isEmpty()) {
                    termFreq.put(token, termFreq.getOrDefault(token, 0) + 1);
                    termDocCount.put(token, termDocCount.getOrDefault(token, 0) + 1);
                }
            }
            docTermFreqs.add(termFreq);
            docLengths.add(tokens.length);
        }
        // 计算平均文档长度
        avgDocLength = docLengths.stream().mapToInt(Integer::intValue).average().orElse(0.0);
    }

    // 执行查询并返回排名结果
    public List<BM25Score> search(String query, int topN) {
        // 分词查询
        String[] queryTokens = NotionalTokenizer.segment(query.toLowerCase()).stream().map(t -> t.word).toArray(String[]::new);
        Map<String, Integer> queryTermFreq = new HashMap<>();
        for (String token : queryTokens) {
            if (!token.trim().isEmpty()) {
                queryTermFreq.put(token, queryTermFreq.getOrDefault(token, 0) + 1);
            }
        }

        // 计算每个文档的 BM25 分数
        List<BM25Score> results = new ArrayList<>();
        for (int docId = 0; docId < documents.size(); docId++) {
            double score = 0.0;
            Map<String, Integer> docTermFreq = docTermFreqs.get(docId);
            int docLength = docLengths.get(docId);

            for (String term : queryTermFreq.keySet()) {
                int tf = docTermFreq.getOrDefault(term, 0);
                int n = termDocCount.getOrDefault(term, 0);
                if (tf > 0) {
                    // 计算 BM25 分数（仅考虑正文，标题和锚文本设为 0）
                    score += bm25.score(
                            tf, docLength, avgDocLength, // 正文
                            0, 0, 0,                    // 标题（无标题数据）
                            0, 0, 0,                    // 锚文本（无锚文本数据）
                            totalDocs, n                // 语料库统计
                    );
                }
            }
            if (score > 0) {
                results.add(new BM25Score(docId, score));
            }
        }

        // 按分数降序排序
        return results.stream()
                .sorted((a, b) -> Double.compare(b.score(), a.score()))
                .limit(topN)
                .toList();
    }

    public List<DocumentDTO> recall(List<BM25Score> searchDocuments) {
        return searchDocuments.stream()
                .map(doc -> {
                    DocumentDTO documentDTO = documents.get(doc.docIndex());
                    return new DocumentDTO(documentDTO.id(), documentDTO.text(), documentDTO.metadata(), doc.score());
                })
                .toList();
    }

    public static void main(String[] args) {
        // 示例语料库
        List<DocumentDTO> documents = Arrays.asList(
                "yo语音进入云游戏无法正常安装游戏，并出现提示",
                "Yo语音APP的“云游戏”入口不见了",
                "“丘比特乐园”玩法，手机端yo语音app的入口不见了",
                "进入云游戏页面提示网络繁忙，尝试更换过网络连接，但还是无法解决。"
        ).stream()
                .map(s -> new DocumentDTO(s, s, null, null))
                .collect(Collectors.toList());

        // 初始化 BM25 搜索
        BM25Search searchEngine = new BM25Search(documents);

        // 执行查询
        String query = "云游戏入口找不到";
        List<BM25Score> results = searchEngine.search(query, 2);

        // 输出结果
        System.out.println("Query: " + query);
        for (BM25Score result : results) {
            if (result.score() > 0) {
                System.out.printf("Document %d: '%s' (Score: %.4f)%n",
                        result.docIndex(), documents.get(result.docIndex()), result.score());
            }
        }
    }
}
