package com.yy.hd.ranking.bm25;

import com.hankcs.hanlp.dictionary.CustomDictionary;
import com.yy.hd.ranking.dto.DocumentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class BM25Retriever implements InitializingBean {

    private final Binder binder;

    public BM25Retriever(Environment environment) {
        binder = Binder.get(environment);
    }

    private static ScheduledExecutorService scheduledExecutor = new ScheduledThreadPoolExecutor(1);

    private static final String BM25_DIC_WORDS = "hanlp.words";

    private static final Map<String, BM25Search> bm25SearchMap = new ConcurrentHashMap<>();

    @Override
    public void afterPropertiesSet() throws Exception {
        scheduledExecutor.scheduleWithFixedDelay(() -> {
            try {
                List<String> words = binder.bindOrCreate(BM25_DIC_WORDS, List.class);
                words.forEach(CustomDictionary::add);
                log.info("load bm25 words success, words:{}", words);
            } catch (Exception e) {
                log.error("load bm25 words fail", e);
            }
        }, 0, 2, TimeUnit.MINUTES);
    }

    public void documents(String searchKey, List<DocumentDTO> documents) {
        log.info("documents searchKey:{}, documents size:{}", searchKey, documents.size());
        bm25SearchMap.put(searchKey, new BM25Search(documents));
    }

    public List<DocumentDTO> search(String searchKey, String query, int topN) {
        log.info("search searchKey:{}, query:{}, topN:{}", searchKey, query, topN);
        BM25Search bm25Search = bm25SearchMap.get(searchKey);
        if (bm25Search == null) {
            log.warn("bm25Search not found, key:{}", searchKey);
            return Collections.emptyList();
        }
        List<BM25Score> searchResult = bm25Search.search(query, topN);
        return bm25Search.recall(searchResult);
    }

}
