package com.yy.hd.ranking.service;

import com.yy.hd.ranking.bm25.BM25Retriever;
import com.yy.hd.ranking.dto.BM25SearchDTO;
import com.yy.hd.ranking.dto.DocumentDTO;
import com.yy.hd.ranking.dto.UpdateDocumentsDTO;
import com.yy.hd.ranking.dto.FineRankingDTO;
import com.yy.hd.ranking.dto.SimilaritySearchDTO;
import com.yy.hd.ranking.huggingface.OnnxEmbeddingRetriever;
import com.yy.hd.ranking.huggingface.OnnxFineRanker;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionTextParser;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class RankingService {

    @Resource
    private BM25Retriever bm25Retriever;

    @Resource
    private OnnxEmbeddingRetriever onnxEmbeddingRetriever;

    @Resource
    private OnnxFineRanker onnxFineRanker;

    public void documents(UpdateDocumentsDTO req) {
        String vectorStoreKey = req.searchKey();
        List<Document> documents = convert(req.documents());
        onnxEmbeddingRetriever.documents(vectorStoreKey, documents);
        bm25Retriever.documents(vectorStoreKey, req.documents());
    }

    public List<DocumentDTO> bm25Search(BM25SearchDTO req) {
        String searchKey = req.searchKey();
        String query = req.query();
        int topN = req.topN();
        return bm25Retriever.search(searchKey, query, topN);
    }

    public List<DocumentDTO> similaritySearch(SimilaritySearchDTO req) {
        String vectorStoreKey = req.vectorStoreKey();
        String query = req.query();
        double similarity = req.similarity();
        int topK = req.topK();
        String filterExpression = req.filterExpression();
        Filter.Expression expression = null;
        if (StringUtils.isNotBlank(filterExpression)) {
            FilterExpressionTextParser parser = new FilterExpressionTextParser();
            expression = parser.parse(filterExpression);
        }
        return convert2DTO(onnxEmbeddingRetriever.similaritySearch(vectorStoreKey, query, similarity, topK, expression));
    }

    public List<DocumentDTO> fineRanking(FineRankingDTO req) {
        List<Document> documents = convert(req.documents());
        String query = req.query();
        double score = req.score();
        int limit = req.limit();
        return convert2DTO(onnxFineRanker.ranking(documents, query, score, limit));
    }

    private List<Document> convert(List<DocumentDTO> documents) {
        return documents.stream()
                .map(document -> Document.builder()
                        .id(document.id())
                        .text(document.text())
                        .metadata(document.metadata())
                        .score(document.score())
                        .build())
                .toList();
    }

    private List<DocumentDTO> convert2DTO(List<Document> documents) {
        return documents.stream()
                .map(document -> new DocumentDTO(document.getId(), document.getText(), document.getMetadata(), document.getScore()))
                .toList();
    }
}
