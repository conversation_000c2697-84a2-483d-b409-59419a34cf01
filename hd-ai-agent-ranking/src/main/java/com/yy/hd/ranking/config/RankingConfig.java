package com.yy.hd.ranking.config;

import ai.onnxruntime.OrtException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yy.hd.ranking.huggingface.OnnxEmbeddingRetriever;
import com.yy.hd.ranking.huggingface.OnnxFineRanker;
import com.yy.hd.ranking.huggingface.OnnxTransformersEmbeddingModel;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.observation.EmbeddingModelObservationConvention;
import org.springframework.ai.model.transformers.autoconfigure.TransformersEmbeddingModelProperties;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class RankingConfig {

    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return mapper;
    }

    @Bean
    public OnnxTransformersEmbeddingModel embeddingModel(Environment environment,
                                                         TransformersEmbeddingModelProperties properties,
                                                         ObjectProvider<ObservationRegistry> observationRegistry,
                                                         ObjectProvider<EmbeddingModelObservationConvention> observationConvention) {
        String onnxModelUri = environment.getProperty("spring.ai.embedding.transformer.onnx.defaultModelUri");
        String onnxTokenizerUri = environment.getProperty("spring.ai.embedding.transformer.tokenizer.defaultUri");
        OnnxTransformersEmbeddingModel embeddingModel = new OnnxTransformersEmbeddingModel(properties.getMetadataMode(),
                observationRegistry.getIfUnique(() -> ObservationRegistry.NOOP), onnxModelUri, onnxTokenizerUri);

        embeddingModel.setDisableCaching(!properties.getCache().isEnabled());
        embeddingModel.setResourceCacheDirectory(properties.getCache().getDirectory());

        embeddingModel.setTokenizerResource(properties.getTokenizer().getUri());
        embeddingModel.setTokenizerOptions(properties.getTokenizer().getOptions());

        embeddingModel.setModelResource(properties.getOnnx().getModelUri());

        embeddingModel.setGpuDeviceId(properties.getOnnx().getGpuDeviceId());
        embeddingModel.setModelOutputName(properties.getOnnx().getModelOutputName());
        observationConvention.ifAvailable(embeddingModel::setObservationConvention);
        return embeddingModel;
    }

    @Bean
    public OnnxEmbeddingRetriever onnxEmbeddingRetriever(EmbeddingModel embeddingModel) {
        return new OnnxEmbeddingRetriever(embeddingModel);
    }

    @Bean
    public OnnxFineRanker onnxFineRanker(Environment environment) throws OrtException {
        String onnxModelUri = environment.getProperty("spring.ai.reranker.transformer.onnx.modelUri");
        String tokenizerUri = environment.getProperty("spring.ai.reranker.transformer.tokenizer.uri");
        return new OnnxFineRanker(onnxModelUri, tokenizerUri);
    }
}
