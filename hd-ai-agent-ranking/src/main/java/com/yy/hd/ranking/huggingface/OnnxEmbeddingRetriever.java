package com.yy.hd.ranking.huggingface;

import ai.djl.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.SimpleVectorStore;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 粗排：向量相似度检索
 */
@Slf4j
public class OnnxEmbeddingRetriever {

    private final EmbeddingModel embeddingModel;

    private static final String INSTRUCTION = "为这个句子生成表示以用于检索相关文章";

    private static final Map<String, VectorStore> vectorStoreMap = new ConcurrentHashMap<>();

    public OnnxEmbeddingRetriever(EmbeddingModel embeddingModel) {
        this.embeddingModel = embeddingModel;
    }

    public void documents(String searchKey, List<Document> documents)  {
        VectorStore vectorStore = SimpleVectorStore.builder(embeddingModel).build();
        vectorStore.add(documents);
        vectorStoreMap.put(searchKey, vectorStore);
    }

    public List<Document> similaritySearch(String vectorStoreKey, String query, double similarity, int topK, Filter.Expression filterExpression) {
        VectorStore vectorStore = vectorStoreMap.get(vectorStoreKey);
        if (vectorStore == null) {
            log.warn("vectorStore not found, key:{}", vectorStoreKey);
            return List.of();
        }
        List<Document> similarityDocuments = vectorStore.similaritySearch(
                SearchRequest.builder()
                        .query(INSTRUCTION + query)
                        .similarityThreshold(similarity)
                        .filterExpression(filterExpression)
                        .topK(topK)
                        .build()
        );
        log.info("similaritySearch documents:{}, query:{}", JsonUtils.toJson(similarityDocuments), query);
        return similarityDocuments;
    }
}
