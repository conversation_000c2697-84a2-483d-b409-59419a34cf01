# AI大模型配置系统

本文档介绍了项目中新增的AI大模型配置系统，支持多提供商、基础/高级模型分层、多API Key配置等功能。

## 📁 文件结构

### 配置文件
- `hd-ai-agent-server/src/main/resources/ai-models-config.yml` - 主配置文件
- `hd-ai-agent-server/src/main/resources/ai-models-example.yml` - 配置示例文件

### Java类文件
- `hd-ai-agent-model/src/main/java/com/yy/hd/model/config/`
  - `AiModelConfig.java` - 主配置类
  - `ModelProviderConfig.java` - 提供商配置类
  - `ModelConfig.java` - 单个模型配置类
  - `UserModelPreference.java` - 用户偏好设置类
- `hd-ai-agent-model/src/main/java/com/yy/hd/model/enums/`
  - `ModelTierEnum.java` - 模型层级枚举
  - `ProviderTypeEnum.java` - 提供商类型枚举
- `hd-ai-agent-model/src/main/java/com/yy/hd/model/properties/`
  - `AiModelProperties.java` - Spring Boot配置属性类
- `hd-ai-agent-model/src/main/java/com/yy/hd/model/service/`
  - `ModelConfigService.java` - 配置管理服务类

## 🚀 主要功能

### 1. 多提供商支持
- **OpenAI**: GPT系列模型
- **Mistral AI**: 欧洲开源模型
- **Anthropic Claude**: Claude系列对话模型
- **智谱AI**: 中文优化的GLM系列模型
- **百度文心**: 百度文心一言系列
- **阿里通义**: 阿里通义千问系列
- **腾讯混元**: 腾讯混元系列
- **字节豆包**: 字节跳动豆包系列
- **Google Gemini**: Google Gemini系列
- **Azure OpenAI**: Microsoft Azure OpenAI服务

### 2. 模型分层管理
- **基础模型 (Basic)**: 响应快速、成本较低、适合日常对话
- **高级模型 (Advanced)**: 推理能力强、功能丰富、适合复杂任务

### 3. 多API Key支持
- 支持为每个提供商配置多个API Key
- 自动负载均衡和故障转移
- 提高系统可用性和并发处理能力

### 4. 用户偏好管理
- 用户可选择默认使用基础模型或高级模型
- 支持页面切换模型层级
- 个性化的任务类型与模型映射
- 成本敏感度和响应速度偏好设置

### 5. 智能路由和重试
- 支持轮询、加权轮询等路由策略
- 自动重试机制和退避策略
- 超时和限流配置

### 6. 监控和告警
- 请求统计和性能监控
- 成本跟踪和预算控制
- 错误率和响应时间告警

## 📖 使用方法

### 1. 基础配置

在 `ai-models-config.yml` 中配置基本信息：

```yaml
ai:
  models:
    default-provider: openai
    user-preferences:
      default-tier: basic
      allow-tier-switching: true
      switching-strategy: manual
    providers:
      openai:
        name: "OpenAI"
        base-url: "https://api.openai.com"
        enabled: true
        api-keys:
          - "sk-proj-your-api-key-1"
          - "sk-proj-your-api-key-2"
        models:
          basic:
            - model-name: "gpt-3.5-turbo"
              display-name: "GPT-3.5 Turbo"
              max-tokens: 4096
              enabled: true
          advanced:
            - model-name: "gpt-4o"
              display-name: "GPT-4o"
              max-tokens: 128000
              enabled: true
```

### 2. Spring Boot集成

在Spring Boot应用中使用配置属性：

```java
@Autowired
private AiModelProperties aiModelProperties;

@Autowired
private ModelConfigService modelConfigService;

// 获取所有启用的提供商
List<ModelProviderConfig> providers = modelConfigService.getEnabledProviders();

// 获取基础模型列表
List<ModelConfig> basicModels = modelConfigService.getBasicModels("openai");

// 根据用户偏好选择模型
UserModelPreference userPreference = getUserPreference(userId);
Optional<ModelSelectionResult> result = modelConfigService.selectBestModel(userPreference, "chat");
```

### 3. 用户偏好设置

创建用户偏好配置：

```java
UserModelPreference preference = UserModelPreference.builder()
    .userId("user123")
    .preferredProvider("openai")
    .preferredTier("basic")
    .autoModelSelection(false)
    .costSensitivity(UserModelPreference.CostSensitivity.MEDIUM)
    .speedPreference(UserModelPreference.SpeedPreference.BALANCED)
    .build();
```

### 4. 任务特定模型配置

为不同任务类型配置专用模型：

```java
Map<String, TaskModelPreference> taskMappings = Map.of(
    "chat", TaskModelPreference.builder()
        .preferredProvider("openai")
        .preferredTier("basic")
        .preferredModel("gpt-3.5-turbo")
        .build(),
    "code", TaskModelPreference.builder()
        .preferredProvider("openai")
        .preferredTier("advanced")
        .preferredModel("gpt-4o")
        .build()
);
```

## 🔧 配置验证

使用配置验证功能确保配置正确：

```java
ConfigValidationResult validation = modelConfigService.validateConfiguration();
if (!validation.isValid()) {
    log.error("Configuration issues: {}", validation.issues());
}
```

## 📊 统计信息

获取模型使用统计：

```java
ModelStats stats = modelConfigService.getModelStats();
log.info("Total providers: {}, Total models: {}", 
    stats.totalProviders(), stats.totalModels());
```

## 🎯 最佳实践

### 1. API Key管理
- 使用环境变量或加密配置存储API Key
- 定期轮换API Key
- 为不同环境配置不同的API Key

### 2. 成本控制
- 合理配置基础模型和高级模型的使用场景
- 设置成本阈值和告警
- 监控各提供商的使用量和费用

### 3. 性能优化
- 根据任务类型选择合适的模型
- 配置合理的超时和重试参数
- 使用多个API Key提高并发能力

### 4. 用户体验
- 提供模型切换界面
- 显示模型特性和成本信息
- 支持用户自定义偏好设置

## 🔄 扩展指南

### 添加新的提供商

1. 在 `ProviderTypeEnum` 中添加新的枚举值
2. 在配置文件中添加提供商配置
3. 实现对应的模型客户端

### 添加新的模型层级

1. 在 `ModelTierEnum` 中添加新的层级
2. 更新配置文件结构
3. 调整相关的业务逻辑

### 自定义路由策略

1. 实现新的路由策略类
2. 在配置中添加策略选项
3. 更新路由工厂类

## 🐛 故障排除

### 常见问题

1. **配置加载失败**
   - 检查YAML语法是否正确
   - 确认配置文件路径
   - 验证Spring Boot配置属性绑定

2. **API调用失败**
   - 验证API Key是否有效
   - 检查网络连接和防火墙设置
   - 确认API基础URL是否正确

3. **模型选择异常**
   - 检查提供商是否启用
   - 确认模型配置是否正确
   - 验证用户偏好设置

### 日志配置

启用详细日志以便调试：

```yaml
logging:
  level:
    com.yy.hd.model: DEBUG
```

## 📝 更新日志

- **v1.0** - 初始版本，支持基础的多提供商配置
- 支持基础/高级模型分层
- 多API Key负载均衡
- 用户偏好管理
- 配置验证和监控

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进这个配置系统。请确保：

1. 遵循现有的代码风格
2. 添加适当的测试用例
3. 更新相关文档
4. 验证配置的向后兼容性
